const messages = document.getElementById('messages')
const gpt_loader = document.getElementById('gpt_loader')
const introText = document.querySelector('.introText')
const defaultOptions = document.querySelector('.defaultOptions')
const isTyping = document.querySelector('.is-typing')
const storepdfState = document.getElementById('storepdfState')
const conversation = document.querySelector('.conversation');
const popupOpt = document.querySelector('.popupOpt')
const closeHNSliderBtn = document.getElementById('closeHNSlider')
const refresHN = document.getElementById('refresHN')
const prevChat = document.getElementById('prevChat')
let cardClicked = false;
let answersCount = 0;
let isSnipQuestion = false;
let imgBS = ""



let systemPromptLabel = "";
let gptLogId = 0
let feedbackType=''
let feedbackText = ''
let temp
let tempQuesQuery=''
let promptTypesTemp

var modal = document.getElementById('feedbackModal');
var feedbackContent = document.getElementById('feedbackContent');
var closeBtn = document.querySelector('.close');
let globalChatHistory = []
let history_for_llm=[]
let isResponseComplete = true
let typingTimeout;
let pageNo = 0
var typingWorker;
let isPdfProcessed = false;
let isdoubtInputEntered = false;
let currImgLink = null
let isClearingChat = false;
let videoIndex = 0
let player;
let lastPlayed = ''
let isMute = false;
let inProgressQue = [];
let isReading = false;
let utterance;
let showReadOption = false;
let currentMcqObjId = null;
let currentMcqObj = null;
let inputMessageLabel = "";
async function getPromptsList(){
    showGptLoader()
    if((bookType==="bookgpt" || bookType==="ebookwithai") && !isTestSeries){
        const promptsRes = await fetch("/prompt/getDefaultPromptListForResource?resId="+gptResId+"&chapterId="+gptChapterId)
        const promptsList = await promptsRes.json()
        const resourcePrompts = JSON.parse(promptsList.gpts)
        if(bookType==="bookgpt"){
            basePrompts = resourcePrompts;
        }else if(bookType==="ebookwithai"){
            basePrompts = []
        }
        suggestedVideos = JSON.parse(promptsList.suggestedVideos)
    }else{
        basePrompts = []
    }
    chatUIHandler()
    if(!isTestSeries){
        getChatHistory()
    }

    hideGptLoader()
    if(!isTestSeries){
        const checkPDF = await fetch('/prompt/checkPDFExists?namespace='+namespace,{
            method:"get"
        })
        const isPDFExist = await checkPDF.json()

        if(!isPDFExist.isExist){
            const pdfres = await fetch("/prompt/storePdfVectors?bookId="+gptBookId+"&chapterId="+gptChapterId+"&resId="+gptResId)
            const pdfStore = await pdfres.json()
            if(pdfStore.status!='OK'){
                alert("Error in storing PDF")
                return
            }else{
                namespace = pdfStore.namespace;
                isPdfProcessed = true
                if(isdoubtInputEntered){
                    storepdfState.style.display = 'none'
                    const chatInput = document.getElementById('chatInput')
                    await askDoubtAPI(chatInput.value, 'userInput')
                }
            }
        }else{
            namespace = isPDFExist.namespace;
            isPdfProcessed = true
        }
    }else{
        isPdfProcessed = true
    }
    if(isTeacher==="true"){
        if(document.querySelector('.teacherTitle')){
            document.querySelector('.teacherTitle').style.display = "block"
        }
    }
}

// check if gptChatperId is defined
if (typeof gptChapterId != 'undefined') {

    getPromptsList()
}


function mergeArrays(defaultPrompts, resourcePrompts, key) {
    var combinedArray = defaultPrompts.concat(resourcePrompts);

    var uniqueArray = combinedArray.filter((obj, index, self) =>
        index === self.findIndex((o) => o[key] === obj[key])
    );

    return uniqueArray;
}

function chatUIHandler() {
    let defaultOptionsList = basePrompts;

    if (defaultOptionsList.length> 0){
        defaultOptionsList = defaultOptionsList.sort(function(a, b) {
            var aIsTeacher = a.promptType.startsWith("teacher_") ? -1 : 1;
            var bIsTeacher = b.promptType.startsWith("teacher_") ? -1 : 1;
            return aIsTeacher - bIsTeacher;
        });
        if(isTeacher==="false"){
            defaultOptionsList = defaultOptionsList.filter(function(item) {
                return !item.promptType.startsWith("teacher_")
            });
        }
        var studyToolsList = document.getElementById('studyToolsList');
        studyToolsList.innerHTML = '';
        defaultOptionsList.forEach(function(option) {
            if (option.promptType !== "chapter_snapshot" &&
                option.promptType !== "mcq" && option.promptType !== "mcqs" &&
                option.promptType !== "qna" && option.promptType !== "qnas"){
                createStudyToolItem(option,"default",[{name:"data-questionId",value:option.id}])
            }
        })
        var promptTypes = defaultOptionsList.map(function(option) {
            return option.promptType
        })
        promptTypesTemp = promptTypes;
        if ((promptTypes.includes('mcq') || promptTypes.includes("mcqs") ||
            promptTypes.includes("qna") || promptTypes.includes("qnas"))&&gptSiteId!="112") {
            createStudyToolItem({
                iconPath:"/assets/resource/testIcon.png",
                promptLabel:"Create Question Paper",
                promptType:"giveTest",
                prompt:"Create Test"
            },"custom",[{name:"data-giveTest",value:true}])
        }
        if(suggestedVideos.length>0){
            createStudyToolItem({
                iconPath:"/assets/resource/suggestedvideos.png",
                promptLabel:"Suggested Videos",
                promptType:"Suggested Videos",
                prompt:"Create suggested videos"
            },"custom",[{name:"data-suggestedvideos",value:true}])
        }
        createStudyToolItem({
            iconPath:"/assets/resource/marker.png",
            promptLabel:"Highlights & Notes",
            promptType:"highlights",
            prompt:"Create Test"
        },"custom",[{name:"data-highlight",value: true}])

        // Add separator line
        var separator = document.createElement('div');
        separator.className = 'study-tool-separator';
        separator.style.cssText = 'border-top: 1px solid #ddd; margin: 10px 0; width: 100%;';
        document.getElementById('studyToolsList').appendChild(separator);

        // Add MCQ and QNA prompts after separator
        defaultOptionsList.forEach(function(option) {
            if (option.promptType === "mcq" || option.promptType === "mcqs" ||
                option.promptType === "qna" || option.promptType === "qnas"){
                createStudyToolItem(option,"default",[{name:"data-questionId",value:option.id}])
            }
        })

        // Add PYQS if promptType includes pyqs
        defaultOptionsList.forEach(function(option) {
            if (option.promptType === "pyqs"){
                createStudyToolItem(option,"default",[{name:"data-pyqs",value:option.id}])
            }
        })
        document.querySelector('.study-tools').style.display = "block";
        document.querySelector('.prompts_dropdown-highlights').style.display = "none"
        checkScroll();
    } else if (defaultOptionsList.length === 0 && !isTestSeries) {
        document.querySelector('.study-tools').style.display = "none";
        document.querySelector('.prompts_dropdown-highlights').style.display = "block"
    }else{
        document.querySelector('.prompts_dropdown-highlights').style.display = "none"
        document.querySelector('.study-tools').style.display = "none";
    }

}
const chatInput = document.getElementById('chatInput')
const sendBtn = document.getElementById('sendBtn')
const startCaptureBtn = document.getElementById('start-capture-btn')
const captureResult = document.getElementById('capture-result')
const snipCancel = document.querySelector('.snipCancel')
snipCancel.addEventListener("click",cancelSnip)

if(gptSiteId=="71"){
    startCaptureBtn.style.display = "none"
}
function captureBtn(){
    return startCaptureBtn
}
function captureResultDiv(){
    return captureResult
}
function snipCancelDiv(){
    return snipCancel
}
captureResult.addEventListener("click", openSnippedImg)

function cancelSnip(){
    captureResult.innerHTML = ""
    captureResult.style.display = "none"
    snipCancel.style.display = "none"
    isSnipQuestion = false
    currImgLink = ""
}

function openSnippedImg(){
    const imgsrc = document.querySelector('#capture-result img').getAttribute("src")
    let htmlsrc = "<div style='display: flex;flex-direction:column;justify-content: center;align-items: center;min-height: 120px'>" +
        "<span onclick='closeFormula()' style='margin-left: auto;margin-bottom: 1rem'><i class='fa-solid fa-xmark'></i></span>"+
        "<img src='"+imgsrc+"' style='width: 100%;'>" +
        "</div>"
    feedbackContent.innerHTML = htmlsrc
    document.querySelector('.modal-content').style.maxWidth='500px'
    openModal()
}

function createStudyToolItem(option, type, attrs) {
    var item = document.createElement('div');
    item.className = 'study-tool-item';

    var img = document.createElement('img');
    if (type === "custom") {
        img.src = option.iconPath;
    } else {
        if (option.iconPath && (option.iconPath.includes(".png") ||
            option.iconPath.includes(".svg") ||
            option.iconPath.includes(".jpg") ||
            option.iconPath.includes(".jpeg"))) {
            var tId = option.iconId ? option.iconId : option.id;
            img.src = "/prompt/showPromptIcon?promptId=" + tId + "&fileName=" + option.iconPath;
        } else {
            img.src = "/assets/resource/testIcon.png";
        }
    }
    item.appendChild(img);

    // Add attributes
    attrs.forEach(function(attr) {
        item.setAttribute(attr.name, attr.value);
    });

    if (!item.getAttribute("data-highlight") && isContentsLocked) {
        item.classList.add('lockedContent');
    }

    var text = document.createTextNode(option.promptLabel);
    item.appendChild(text);

    item.addEventListener('click', function() {
        var details = document.querySelector('.study-tools');
        details.removeAttribute('open');
        if (!isResponseComplete) {
            pauseShowingAnswer();
        }

        if (item.getAttribute('data-giveTest')) {
            showGiveTestInputModal();
        } else if (item.getAttribute("data-suggestedvideos")) {
            showUserMessage(option.promptLabel, false, '');
            isTyping.style.display = 'flex';
            conversation.scrollTop = conversation.scrollHeight;
            setTimeout(function() {
                videoResponseHandler(suggestedVideos);
            }, 3000);
        } else if (item.getAttribute("data-highlight")) {
            getHighlightedData();
        } else {
            isResponseComplete = false;
            systemPromptLabel = option.promptLabel;
            getDefaultAnswer(
                option.promptLabel.replaceAll("&", "and"),
                option.prompt,
                option.promptType
            );
        }
    });

    document.getElementById('studyToolsList').appendChild(item);
}

const formulaBtn = document.querySelector('.formulaBtn')
formulaBtn.addEventListener('click',()=>{
    let inputHTML = "<div class='feedbackOptions'>" +
        "<div class='closeBtn'>" +
        "<span onclick='closeFormula()'><i class='fa-solid fa-xmark'></i></span>"+
        "</div>"+
        "<div class='formulaModalContent'>" +
        "<p>Write Formula LaTex</p>" +
        "<textarea id='formulaInputField'></textarea>" +
        "</div>" +
        "<div id='formulaPreviewDiv' class='formulaPreviewDiv'>" +
        "</div>"+
        "<div class='formulaActions'>" +
        "<div>" +
        "<button class='addFormula'>Add Formula</button>" +
        "</div>" +
        "<a href='https://en.wikibooks.org/wiki/LaTeX/Mathematics' target='_blank'>LaTex Documentation</a> "+
        "</div>"+
        "</div>";
    feedbackContent.innerHTML = inputHTML;
    openModal()

    const addFormula = document.querySelector('.addFormula')
    const formulaInputField = document.getElementById('formulaInputField')
    const formulaPreviewDiv = document.getElementById('formulaPreviewDiv')
    formulaInputField.value = "\\[ x^n + y^n = z^n \\]"
    renderFormula()
    addFormula.addEventListener('click',()=>{
        if(formulaInputField.value.trim()!=""){
            const chatInp = document.getElementById('chatInput')
            const cursorPosition = chatInp.selectionStart;
            const value = chatInp.value;
            const newValue = formulaInputField.value
            chatInp.value = newValue
            chatInp.setSelectionRange(cursorPosition + formulaInputField.value.length, cursorPosition + formulaInputField.value.length);
        }
        closeFormula()
    })
    function renderFormula() {
        try {
            formulaPreviewDiv.textContent = formulaInputField.value.trim()
            renderMathInElement(formulaPreviewDiv);
        } catch (error) {
            formulaPreviewDiv.innerHTML = "Invalid formula";
        }
    }
    formulaInputField.addEventListener('input', renderFormula);
})
function closeFormula(){
    setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('fade-out');
    }, 500);
}


    if(sendBtn){
        sendBtn.addEventListener('click',(e)=>{
            if(isResponseComplete){
                if(chatInput.value.trim()!=='' || isSnipQuestion){
                    // const divElement = document.querySelector('.prompts_dropdown');
                    // const isOpen = divElement.classList.contains('open');
                    // if(isOpen){
                    //     togglePromptsDropdown()
                    // }
                    isdoubtInputEntered = true
                    if(isTestSeries){
                        if(checkTokens()){
                            askTest(chatInput.value,'userInput')
                        }else {
                            showBuyPopup()
                        }
                    }else{
                        askDoubts(chatInput.value,'userInput')
                    }
                    auto_shrink(chatInput)
                }
            }else{
                pauseShowingAnswer()
            }
        })
    }
    if(chatInput){
        chatInput.addEventListener('keydown',(e)=>{
            if(e.key === 'Enter' && !event.shiftKey){
                event.preventDefault();

                if(isResponseComplete){
                    if(chatInput.value.trim()!=='' || isSnipQuestion){
                        // const divElement = document.querySelector('.prompts_dropdown');
                        // const isOpen = divElement.classList.contains('open');
                        // if(isOpen){
                        //     togglePromptsDropdown()
                        // }
                        isdoubtInputEntered = true
                        if(isTestSeries){
                            if(checkTokens()){
                                askTest(chatInput.value,'userInput')
                            }else {
                                showBuyPopup()
                            }
                        }else{
                            askDoubts(chatInput.value,'userInput')
                        }
                        auto_shrink(chatInput)
                    }
                }else{
                    pauseShowingAnswer()
                }
            }
        })
    }

function pauseShowingAnswer(){
    if (typingWorker) {
        typingWorker.terminate();
        typingWorker = null;  // Reset the worker reference
    }
    isResponseComplete = true;
    document.getElementById("feedbackWrap_" + answersCount).style.display = 'block';
    document.getElementById('sendBtn').innerHTML = "<i class='fa-solid fa-paper-plane'></i>"
    conversation.scrollTop = conversation.scrollHeight
}

function askTest(query, resType){
    interactWithTest(currentMcqObj, resType, currentMcqObjId, query)
}

async function askDoubts(query,resType){
    const checkupBtn = document.querySelector('.gptBuyNowBtnNew')
    if(!mobileView){
        if(!checkupBtn && isContentsLocked){
            const upBtn = document.createElement('button');
            upBtn.classList.add('gptBuyNowBtnNew');
            upBtn.setAttribute('onClick','openBookDtlPage()');
            upBtn.textContent = 'Upgrade Now';
            defaultOptions.prepend(upBtn)
        }
    }
    isResponseComplete = false
    conversation.removeAttribute('style')
    showUserMessage(query)
    isTyping.style.display='flex'
    if(!isPdfProcessed){
        storepdfState.style.display='flex'
        isTyping.style.width='200px'
    }else{
        storepdfState.style.display='none'
        isTyping.style.width='50px'
    }
    conversation.scrollTop = conversation.scrollHeight
    if(globalChatHistory.length>0) {
        globalChatHistory.forEach((chat) => {
            history_for_llm.push({
                user: chat.userPrompt ? chat.userPrompt : "",
                ai: chat.response ? chat.response : ""
            });
        })
    }
    if(isPdfProcessed && isdoubtInputEntered){
        await askDoubtAPI(query,resType)
    }
}

function getSnippedImg(imgblob){
    imgBS = imgblob
    isSnipQuestion = true
}
const askDoubtAPI = async (query,resType)=>{
    let response = ""

    let reqObj = {
        namespace:namespace,
        query:query,
        resType:resType,
        chatHistory:history_for_llm,
        resId: gptResId,
        chapterId:gptChapterId,
        bookId:gptBookId
    }
    isSnipQuestion ? reqObj.imgData = imgBS : ""
    isSnipQuestion ? reqObj.site = window.location.origin : ""

    captureResult.innerHTML = ""
    captureResult.style.display = "none"
    snipCancel.style.display = "none"

    response = await fetch('/prompt/retrieveData',{
        method:"POST",
        body:JSON.stringify(reqObj),
        headers:{
            "Content-Type":"application/json"
        }
    })
    const answer = await response.json()
    answer.resType = "userInput"
    history_for_llm.push({
        user: query,
        ai: answer.answer,
        imgLink:answer.imgLink ? answer.imgLink : ""
    });
    currImgLink = answer.imgLink ? answer.imgLink : null
    showAnswer(answer,query,true)
    document.getElementById('chatInput').value=''
    globalChatHistory =[]
    isdoubtInputEntered = false
    isSnipQuestion = false
}

async function getDefaultAnswer(quesShortQuery,quesQuery,resType){
    conversation.removeAttribute('style')
    showUserMessage(quesShortQuery,false,'')
    isTyping.style.display='flex'
    conversation.scrollTop = conversation.scrollHeight

    skipDefaultHandler = true;
    pageNo = 0
    await getDefaultAnswerHandler(quesShortQuery, quesQuery, resType)
}

async function getDefaultAnswerHandler(quesShortQuery, quesQuery, resType) {
    try{
        const response = await fetch("/prompt/getGPTsForResource?resId="+gptResId+"&promptType="+resType)
        const responseData = await response.json()
        responseData.resType = resType

        if(responseData.hasResource){
            if((resType.includes("mcq") || resType.includes("mcqs")) ||
                (resType.includes("flashcards") || resType.includes("flashcard")) ||
                (resType.includes("qna") || resType.includes("qanda"))){
                showInputModal(responseData,quesQuery,resType)
            }else{
                showAnswer(responseData,quesQuery,true)
                history_for_llm.push({
                    user: quesShortQuery,
                    ai: responseData.answer
                });
            }
        }else{

            skipDefaultHandler = false;
            setUpChapter(resType)
        }
    }catch (err){
        console.log(err)
    }
}

async function setUpChapter(resType){
    const chapterId = gptChapterId
    try {
        var formData = new FormData();
        formData.append('chapterId', chapterId);
        var response = await fetch("/autogpt/setUpChapter",{
            method: 'POST',
            body: formData
        })

        if (response.ok) {
            var data = await response.json();
            if(data.status == "uploaded"){
                await createAndUpdateGPTContent(data, resType)
            }else{
                console.log("something went wrong")
            }
        } else {
            console.error('Failed to trigger prompt');
        }
    } catch (error) {
        console.error('Error:', error);
    }
}

async function createAndUpdateGPTContent(data, resType){
    try {
        const requestedObj = findElement(basePrompts, "promptType", resType)
        if(findElement(inProgressQue, 'promptType', resType)){
            retryDefault(requestedObj)
        }else{
            const chapterId = gptChapterId
            var formData = new FormData();
            formData.append('namespace', data.namespace);
            formData.append('promptType', resType);
            formData.append('readingMaterialResId', data.readingMaterialResId);
            formData.append('chapterId', chapterId);

            inProgressQue.push({
                type:resType,
                readingMaterialResId:data.readingMaterialResId
            })
            var response = await fetch("/autogpt/createAndUpdateGPTContent",{
                method: 'POST',
                body: formData
            })

            if (response.ok) {
                var data = await response.json();

                if(data.response == "Added"){

                    removeElement(inProgressQue, 'promptType', resType)
                    if (!skipDefaultHandler) {
                        await getDefaultAnswerHandler('', '', resType);
                    }
                }else if(data.response == "In process") {
                    retryDefault(requestedObj)
                }
            } else {
                console.error('Failed to trigger prompt');
            }
        }

    } catch (error) {
        console.error('Error:', error);
    }
}
async function retryDefault(requestedObj){
    setTimeout(async ()=>{
        await getDefaultAnswerHandler(requestedObj.promptLabel, requestedObj.basePrompt, resType)
    },1000)
}
function findElement(array, key, value) {
    return array.find(function(element) {
        return element[key] === value;
    });
}

function removeElement(array, key, value) {

    var index = array.findIndex(function(element) {
        return element[key] === value;
    });

    if (index !== -1) {
        return array.splice(index, 1)[0];
    }

    return null;
}
function showInputModal(responseData,quesQuery,resType){
    isTyping.style.display='none'
    let quizId
    const resId = responseData.resId
    temp = responseData
    tempQuesQuery = quesQuery

    let label =""
    let showDifficulty = false;
    let placeholder = ""
    let type  = ""
    if((resType.includes("mcq") || resType.includes("mcqs")) ||
        (resType.includes("qna") || resType.includes("qanda")) ||
        (resType.includes("pyqs"))){
        label = "Number of Questions"
        placeholder = "Enter number of questions"
        type ="mcqs"
        quizId = responseData.quizId
        if(resType.includes("qna") || resType.includes("qanda")){
            type="qna"
        }
        if(resType.includes("pyqs")){
            type="pyqs"
        }
    }else if(resType.includes("flashcards") || resType.includes("flashcard")){
        label = "Number of Flashcards"
        placeholder = "Enter number of flashcards"
        showDifficulty = false
        type ="flashcards"
    }
    let mcqInputHTML = "<div>" +
        "<i class='fa-solid fa-xmark' id='noticeClose' onclick='closeNotice()'></i>" +
        "<div class='inputGroup' style='flex-direction: column!important;align-items: flex-start !important;'>" +
        "<label for='noOfQuiz' style='width: 50% !important;'>"+label+"</label>";
        mcqInputHTML +="<input type='number' id='noOfQuiz' placeholder='"+placeholder+"' style='width: 92% !important;'>";

    mcqInputHTML +="</div>";
    if(showDifficulty){
        mcqInputHTML += "<div class='inputGroup'>" +
            "<label for='difficultyLvl'>Choose Difficulty Level</label>" +
            "<select id='difficultyLvl'>" +
            "<option value='Easy'>Easy</option>"+
            "<option value='Medium'>Medium</option>"+
            "<option value='Hard'>Hard</option>"+
            "</select>"+
            "</div>";
    }
    mcqInputHTML +="<div class='createBtn'>";
    if(type=="mcqs"){
        mcqInputHTML +="<button id='createMcqsBtn' onclick=\"getDefaultMcqs('"+quizId+"','"+resId+"')\">Create MCQs</button>";
    }else if(type=="flashcards"){
        mcqInputHTML +="<button id='createMcqsBtn' onclick=\"getDefaultFlashcards('"+resId+"')\">Create Flashcards</button>";
    }else if(type=="qna"){
        mcqInputHTML +="<button id='createMcqsBtn' onclick=\"getDefaultQuestionAndAns('"+quizId+"','"+resId+"')\">Create Questions</button>";
    }else if(type=="pyqs"){
        mcqInputHTML +="<button id='createMcqsBtn' onclick=\"getDefaultPyqs('"+quizId+"','"+resId+"')\">Create PYQS</button>";
    }
    mcqInputHTML +="</div>" +
        "</div>";
    feedbackContent.innerHTML = mcqInputHTML
    document.querySelector('.modal-content').style.maxWidth='500px'
    openModal()
}

async function showGiveTestInputModal() {
    showUserMessage("Create Question Paper");
    const response = await fetch('/prompt/getTestInfo?readingMaterialResId='+gptResId)
    if(!response.ok){
        isTyping.style.display = 'none';
        isResponseComplete = true
        alert("Something went wrong.")
    }
    const result = await response.json()


    let mcqInputHTML = "<div>" +
        "<i class='fa-solid fa-xmark' id='noticeClose' onclick='closeNotice()'></i>";
    if (promptTypesTemp.includes('mcq') || promptTypesTemp.includes("mcqs")) {
        const difficultylevelsMcq = result.mcqs.difficultylevels
        if(difficultylevelsMcq){
            mcqInputHTML +="<p><strong>MCQs</strong></p>"+
                "<div style='padding-right: 8px'>";
            const diffValues = Object.keys(difficultylevelsMcq)
            diffValues.forEach(diff=>{
                mcqInputHTML +="<div class='inputGroup'>" +
                    "<label for='"+diff+"_mcq' class='_mcq-label'>"+diff+" ("+difficultylevelsMcq[diff]+")</label>" +
                    "<input type='number' id='"+diff+"_mcq' class='_mcq' data-type='"+diff+"' value='0'>" +
                    "</div>";
            })
            mcqInputHTML +="</div>"+
                "</div>";
        }
    }

    if (promptTypesTemp.includes('qna') || promptTypesTemp.includes("qnas")) {
        const difficultylevelsQnA = result.qna.difficultylevels
        if(difficultylevelsQnA){
            mcqInputHTML +="<p><strong>Questions</strong></p>"+
                "<div style='padding-right: 8px'>";
            const diffValues = Object.keys(difficultylevelsQnA)
            diffValues.forEach(diff=>{
                mcqInputHTML +="<div class='inputGroup'>" +
                    "<label for='"+diff+"_qna' class='_qna-label'>"+diff+" ("+difficultylevelsQnA[diff]+")</label>" +
                    "<input type='number' id='"+diff+"_qna' class='_qna' data-type='"+diff+"' value='0'>" +
                    "</div>";
            })
            mcqInputHTML +="</div>"+
                "</div>";
        }
    }

    mcqInputHTML += "<div class='createBtn'>"+
        "<p class='testErrMsg' style='color: red;font-weight: bold;margin-bottom: 10px !important;'></p>";
    mcqInputHTML += "<button id='createMcqsBtnTest'>Create Question Paper</button>";

    mcqInputHTML += "</div>" +
        "</div>";
    feedbackContent.innerHTML = mcqInputHTML;
    const createMcqsBtn = document.getElementById('createMcqsBtnTest');
    createMcqsBtn.addEventListener('click', function handleCreateMcqsClick() {
        getTestQuestions(gptResId, result);
    });
    document.querySelector('.modal-content').style.maxWidth = '500px';
    openModal();
}

async function getTestQuestions(resId, result) {
    const levelsQnAValidation = result.qna.difficultylevels
    const levelsMCQValidation = result.mcqs.difficultylevels
    const errMsgItem = document.querySelector('.testErrMsg')
    const queryParams = new URLSearchParams({
        readingMaterialResId: resId,
    });

    const qnaEl = document.querySelectorAll('._qna')
    const mcqEl = document.querySelectorAll('._mcq')

    let isValid = false;

    qnaEl.forEach(el => {
        const value = parseInt(el.value, 10) || 0;
        if (value > 0) {
            isValid = true;
        }

        if (levelsQnAValidation){
            const cc = value
            if (levelsQnAValidation[el.dataset.type] < cc || el.value.trim() === "") {
                errMsgItem.innerHTML = "Please enter a valid number of questions for " + el.dataset.type;
                setTimeout(() => {
                    errMsgItem.innerHTML = "";
                }, 3000);
                return;
            }
        }
        queryParams.set(el.id, el.value);
    })

    mcqEl.forEach(el=>{
        const value = parseInt(el.value, 10) || 0;
        if (value > 0) {
            isValid = true;
        }
        if(levelsMCQValidation){
            const cc = el.value? el.value:0
            if(levelsMCQValidation[el.dataset.type] < cc || el.value.trim()==""){
                errMsgItem.innerHTML = "Please enter a valid number of questions for "+el.dataset.type
                setTimeout(()=>{
                    errMsgItem.innerHTML = ""
                },3000)
                return;
            }
        }
        queryParams.set(el.id, el.value);
    })

    if (!isValid) {
        errMsgItem.innerHTML = "Please fill in at least one field.";
        setTimeout(() => {
            errMsgItem.innerHTML = "";
        }, 8000);
        return;
    }

    if (errMsgItem.innerHTML !== "") return;
    setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('fade-out');
    }, 500);

    try {
        const quizRes = await fetch(`/prompt/createTest?${queryParams.toString()}`);
        const quizData = await quizRes.json();

        isTyping.style.display = 'flex';
        conversation.scrollTop = conversation.scrollHeight;

        temp = {
            ...temp,
            quizData,
        };

        setTimeout(function () {
            showTest(temp, false, '');
        }, 1000);
    } catch (error) {
        console.error("Error fetching test questions:", error);
        alert("Failed to fetch test questions. Please try again.");
    }
}
async function getDefaultMcqs(quizId,resId){
    const noOfQuiz = document.getElementById('noOfQuiz').value
    if(noOfQuiz=="" || noOfQuiz==" " || noOfQuiz==0){
        alert("Please enter number of questions")
        return
    }

    setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('fade-out');
    }, 500);

    const quizRes = await fetch('/funlearn/newQuizQA?quizId='+quizId+'&resId='+resId+'&siteId=1&noOfQuestions='+noOfQuiz)
    const quizData = await quizRes.json()
    const questions = quizData.results
    isTyping.style.display='flex'
    conversation.scrollTop = conversation.scrollHeight
    temp.mcqs = questions
    temp.noOfQuestions= noOfQuiz
    setTimeout(function (){
        showAnswer(temp,tempQuesQuery,true)
    },1000)
}

async function getDefaultFlashcards(resId){
    const noOfQuiz = document.getElementById('noOfQuiz').value

    if(noOfQuiz=="" || noOfQuiz==" " || noOfQuiz==0){
        alert("Please enter number of questions")
        return
    }
    const flashcardRes = await fetch('/funlearn/getFlashCards?resId='+resId+'&name='+temp.resourceName+'&noOfQuestions='+noOfQuiz)
    const flashcardData = await flashcardRes.json()

    const flashcards = flashcardData.keyValues
    isTyping.style.display='flex'
    conversation.scrollTop = conversation.scrollHeight
    temp.flashcards = flashcards
    temp.noOfQuestions= noOfQuiz
    setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('fade-out');
    }, 500);
    setTimeout(function (){
        showAnswer(temp,tempQuesQuery,true)
    },1000)
}

async function getDefaultQuestionAndAns(quizId,resId){
    const noOfQuiz = document.getElementById('noOfQuiz').value
    if(noOfQuiz=="" || noOfQuiz==" " || noOfQuiz==0){
        alert("Please enter number of questions")
        return
    }
    setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('fade-out');
    }, 500);

    const quizRes = await fetch('/prompt/getQuestionAndAnswers?resId='+resId+'&noOfQuestions='+noOfQuiz)
    const quizData = await quizRes.json()

    const questions = quizData.qaList
    isTyping.style.display='flex'
    conversation.scrollTop = conversation.scrollHeight
    temp.qaList = questions
    setTimeout(function (){
        showAnswer(temp,tempQuesQuery,true)
    },1000)

}

async function getDefaultPyqs(quizId,resId){
    const noOfQuiz = document.getElementById('noOfQuiz').value
    if(noOfQuiz=="" || noOfQuiz==" " || noOfQuiz==0){
        alert("Please enter number of questions")
        return
    }
    setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('fade-out');
    }, 500);

    const quizRes = await fetch('/prompt/getPyqs?resId='+resId+'&noOfQuestions='+noOfQuiz)
    const quizData = await quizRes.json()

    const questions = quizData.qaList
    isTyping.style.display='flex'
    conversation.scrollTop = conversation.scrollHeight
    temp.qaList = questions
    temp.resType = "pyqs"
    setTimeout(function (){
        showAnswer(temp,tempQuesQuery,true)
    },1000)

}
function showUserMessage(question,isHistory,historyObj){
    if(question){
        question = question.replace(/\\text{([^}]*)}/g, '$1');
    }
    answersCount++
    let userMessageHTML = ""
    userMessageHTML +=
        "<div class='studentMessageWrapper'>"+
        "<div class='message userMessage' id='userQue_"+answersCount+"'>";
        if(isSnipQuestion && !isHistory){
            userMessageHTML +="<img src='"+imgBS+"' style='width: 100px;height: 100px;object-fit: contain'><br/>";
        }else if(isHistory && historyObj.imgLink){
            userMessageHTML +="<img src='/funlearn/downloadEpubImage?source="+historyObj.imgLink+"' style='width: 100px;height: 100px;object-fit: contain'><br/>";
        }
        if(question){
            userMessageHTML += question;
        }

    userMessageHTML +="</div>"+
        "<div class='studentIcon'>";
    if(profilePic==null || profilePic=='null' || !profilePic){
        userMessageHTML +="<img src='/assets/resource/student-icon.svg'/>";
    }else if(profilePic!=null && profilePic!='null' && profilePic){
        userMessageHTML += "<img src='/funlearn/showProfileImage?id="+userId+"&fileName="+profilePic+"&type=user&imgType=passport'/>";
    }
    userMessageHTML +="</div>"+
        "</div>";
    messages.innerHTML += userMessageHTML
    if(question){
        const ansDiv = document.getElementById("userQue_"+answersCount+"")
        const txt = ansDiv.textContent
        if(/\\\\x\^n \+ y\^n = z\^n \\\\/.test(txt)){
            const updatedString = txt.replace(/\\\\x\^n \+ y\^n = z\^n \\\\/g, '\\[x^n + y^n = z^n \\]');
            ansDiv.textContent = updatedString
        }
        if(ansDiv){
            renderMathInElement(ansDiv);
        }
    }
    conversation.scrollTop = conversation.scrollHeight + 200
    if(isHistory){
        showAnswer(historyObj,question,false)
    }
}

function showAnswer(answer,quesQuery,showTyping,preChat){
    const promptType = answer.resType;

    if(isTextRes(promptType)){
        textResponseHandler(answer,showTyping,preChat)
    }else if(isVideoRes(promptType)){
        videoResponseHandler(answer)
    }else if(isFlashcardRes(promptType)){
        flashcardResponseHandler(answer)
    }else if(isMCQRes(promptType)){
        mcqAnswerHandler(answer)
    }else if(isQnARes(promptType)){
        qnaResponseHandler(answer)
    }else if(isPyqsRes(promptType)){
        pyqsResponseHandler(answer)
    }

    if(showTyping && promptType=="userInput"){
        storeQueryAndResponse(quesQuery,answer.answer,promptType,answersCount)
    }

    const customfeedbackInput = document.querySelectorAll('.customfeedbackInput')

    if(customfeedbackInput.length>0){
        customfeedbackInput.forEach(inp=>{
            inp.innerHTML = ""
        })
    }
}

function isTextRes(promptType){
    if((promptType!=='mcq' && promptType!=='MCQ' && promptType!=='MCQs' && promptType!=='mcqs') &&
        (promptType!=='flashcard' && promptType!=='flashcards' && promptType!=='Flashcard') &&
        (promptType!=='videos' && promptType!=='reference videos' && promptType!=='' && promptType!=='Videos') &&
        (promptType!=='qna' && promptType!=='question and answer' && promptType!=='QnA')){
        return true;
    }
}

function isMCQRes(promptType){
    if((promptType =='mcq' || promptType =='MCQ' || promptType =='MCQs') || promptType =='mcqs' ){
        return true
    }
}

function isFlashcardRes(promptType){
    if((promptType =='flashcard' || promptType =='flashcards' || promptType =='Flashcard' || promptType =='Flashcards')){
        return true
    }
}

function isVideoRes(promptType){
    if((promptType =='videos' || promptType =='suggestedVideos' || promptType =='reference videos' || promptType =='' || promptType =='Videos')){
        return true
    }
}
function isQnARes(promptType){
    if((promptType =='qna' || promptType =='question and answer' || promptType =='qanda' || promptType =='question and answers' || promptType =='qnas')){
        return true
    }
}

function isPyqsRes(promptType){
    if(promptType =='pyqs'){
        return true
    }
}

function showTest(tempRes,reset,ansCount){
    if(reset){
        answersCount = ansCount
    }
    const qaCount = answersCount
    temp = tempRes
    const quizData = tempRes.quizData
    const mcqs = quizData.mcqs
    const qna = quizData.qna
    isTyping.style.display='none'
    let botMessageHTML = ""

    let questionHTML = ''
    let qCount = 0
    for(var q=0;q<mcqs.length;q++){
        qCount++
        questionHTML+= "<p>Q"+qCount+". "+mcqs[q].question+"</p>";
        questionHTML+= "<p>A. "+mcqs[q].option1+"</p>"
        questionHTML+= "<p>B. "+mcqs[q].option2+"</p>"
        questionHTML+= "<p>C. "+mcqs[q].option3+"</p>"
        questionHTML+= "<p>D. "+mcqs[q].option4+"</p>"
        questionHTML+= "<br/>"
    }
    for(var q=0;q<qna.length;q++){
        qCount++
        questionHTML+= "<p>Q"+qCount+". "+qna[q].question+"</p>"
        questionHTML+= "<br/>"
    }

    questionHTML+= "<button class='nonText_options_btn' onclick=\"showTestAnswers("+answersCount+")\" id='showAns_"+answersCount+"'>Show Answers</button>"

    const hisGptLogId = tempRes.id
    const hisfeedbackType = tempRes.feedbackType
    if(reset){
        const tempEl = document.createElement("div")
        tempEl.innerHTML = questionHTML
        renderMathInElement(tempEl, {
            delimiters: [
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true }
            ]
        });
        document.getElementById("mesPlaceholder_"+answersCount+"").innerHTML = tempEl.innerHTML
        history_for_llm.push({
            user: document.getElementById("userQue_"+qaCount).textContent,
            ai: document.getElementById("ansDiv_"+qaCount).textContent
        });
    }else {
        const tempEl = document.createElement("div")
        tempEl.innerHTML = questionHTML
        renderMathInElement(tempEl, {
            delimiters: [
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true }
            ]
        });
        questionHTML = tempEl.innerHTML
        botMessageHTML +=
            "<div id='ans_" + answersCount + "' class='botAnswerWrapper'>" +
            "<div class='botTutorIcon'>" +
            "<img src='/assets/resource/tutor-icon.svg'/>" +
            "</div>" +
            "<div style='width: 100%;'>" +
            "<div class='message botMessage' id='ansDiv_" + answersCount + "'>" +
            "<pre id='mesPlaceholder_" + answersCount + "'>" + questionHTML + "</pre>" +
            "</div>" +
            "<div class='feedbackWrap' id='feedbackWrap_" + answersCount + "'>" +
            "<div style='display: flex;align-items: center;gap: 10px'>";
            if(showReadOption){
                botMessageHTML += "<span class='readOtp' id='readOtp_"+answersCount+"'  onclick=readResponse('"+answersCount+"')>" +
                    "<i class='fa-regular fa-circle-play'></i>" +
                    "</span>";
            }
        if (hisfeedbackType != null && hisfeedbackType == 'like') {
            botMessageHTML += "<span><i class='fa-solid fa-thumbs-up' id='feedback_like_" + answersCount + "' disabled='true' data-gptLogId='" + hisGptLogId + "' data-id=" + answersCount + " onclick=likeResHandler(event,'" + answersCount + "')></i></span>" +
                "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_" + answersCount + "' data-gptLogId='" + hisGptLogId + "' data-id=" + answersCount + " onclick=dislikeResHandler(event,'" + answersCount + "')></i></span>";
        } else if (hisfeedbackType != null && hisfeedbackType == "dislike") {
            botMessageHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_" + answersCount + "' data-gptLogId='" + hisGptLogId + "' data-id=" + answersCount + " onclick=likeResHandler(event,'" + answersCount + "')></i></span>" +
                "<span><i class='fa-solid fa-thumbs-down' id='feedback_dislike_" + answersCount + "' data-gptLogId='" + hisGptLogId + "' disabled='true' data-id=" + answersCount + " onclick=dislikeResHandler(event,'" + answersCount + "')></i></span>";
        } else if (hisfeedbackType == null) {
            botMessageHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_" + answersCount + "' data-gptLogId='" + hisGptLogId + "' data-id=" + answersCount + " onclick=likeResHandler(event,'" + answersCount + "')></i></span>" +
                "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_" + answersCount + "' data-gptLogId='" + hisGptLogId + "' data-id=" + answersCount + " onclick=dislikeResHandler(event,'" + answersCount + "')></i></span>";
        }
        if(gptSiteId=="71"){
            botMessageHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"' style='display: none'  onclick=printContent('"+answersCount+"')>";
        }else{
            botMessageHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"'  onclick=printContent('"+answersCount+"')>";
        }
        botMessageHTML +="<i class='fa-solid fa-print'></i>" +
            "<span class='printToolTip' >Print chat</span>"+
            "</span>";
        botMessageHTML += "</div>" +
            "<div id='feedbackInput_" + answersCount + "'></div>" +
            "</div>" +
            "</div>" +
            "</div>";
        messages.innerHTML += botMessageHTML
        conversation.scrollTop = conversation.scrollHeight
        history_for_llm.push({
            user: document.getElementById("userQue_"+qaCount).textContent,
            ai: document.getElementById("ansDiv_"+qaCount).textContent
        });
        isResponseComplete = true
    }
}
function showTestAnswers(id){
    const shBtn = document.getElementById("showAns_"+id+"")
    shBtn.style.width = '160px'
    shBtn.textContent = 'Preparing Answers...'
    setTimeout(()=>{
        shBtn.textContent = 'Reset Test'
    },4000)
    const quizData = temp.quizData
    const mcqs = quizData.mcqs
    const qna = quizData.qna

    let questionHTML = ''
    let qCount = 0
    for(var q=0;q<mcqs.length;q++){
        qCount++
        questionHTML+= "<p>Q"+qCount+". "+mcqs[q].question+"</p>";
        questionHTML+= "<p>A. "+mcqs[q].option1+"</p>"
        questionHTML+= "<p>B. "+mcqs[q].option2+"</p>"
        questionHTML+= "<p>C. "+mcqs[q].option3+"</p>"
        questionHTML+= "<p>D. "+mcqs[q].option4+"</p>"

        if(mcqs[q].answer1 && mcqs[q].answer1 === "Yes"){
            questionHTML+= "<div class='testAns'>" +
                "<span>Answer: A. "+mcqs[q].option1+"</span>" +
                "<i class='fa-solid fa-circle-check'></i>";
            if(mcqs[q].answerDescription){
                questionHTML+="<p>Explanation: "+mcqs[q].answerDescription+"</p>";
            }
            questionHTML+="</div>"
        }else if(mcqs[q].answer2 && mcqs[q].answer2 === "Yes"){
            questionHTML+= "<div class='testAns'>" +
                "<span>Answer: B. "+mcqs[q].option2+"</span>" +
                "<i class='fa-solid fa-circle-check'></i>";
            if(mcqs[q].answerDescription){
                questionHTML+="<p>Explanation: "+mcqs[q].answerDescription+"</p>";
            }
            questionHTML+="</div>"
        }else if(mcqs[q].answer3 && mcqs[q].answer3 === "Yes"){
            questionHTML+= "<div class='testAns'>" +
                "<span>Answer: C. "+mcqs[q].option3+"</span>" +
                "<i class='fa-solid fa-circle-check'></i>";
            if(mcqs[q].answerDescription){
                questionHTML+="<p>Explanation: "+mcqs[q].answerDescription+"</p>";
            }
            questionHTML+="</div>"
        }else if(mcqs[q].answer4 && mcqs[q].answer4 === "Yes"){
            questionHTML+= "<div class='testAns'>" +
                "<span>Answer: D. "+mcqs[q].option4+"</span>" +
                "<i class='fa-solid fa-circle-check'></i>";
            if(mcqs[q].answerDescription){
                questionHTML+="<p>Explanation: "+mcqs[q].answerDescription+"</p>";
            }
            questionHTML+="</div>"
        }
        questionHTML+= "\n\n"
    }
    for(var q=0;q<qna.length;q++){
        qCount++
        questionHTML+= "<p>Q"+qCount+". "+qna[q].question+"</p>"
        questionHTML+= "<span class='testAns'>Answer: "+qna[q].answer+"<i class='fa-solid fa-circle-check'></i></span>"
        questionHTML+= "<br/>"
    }

    questionHTML+= "<button class='nonText_options_btn' onclick=\"resetTestAnswers("+answersCount+")\" id='showAns_"+answersCount+"'>Reset Test</button>"
    const tempEl = document.createElement("div")
    tempEl.innerHTML = questionHTML
    renderMathInElement(tempEl, {
        delimiters: [
            { left: "\\(", right: "\\)", display: false },
            { left: "\\[", right: "\\]", display: true }
        ]
    });
    questionHTML = tempEl.innerHTML
    document.getElementById("mesPlaceholder_"+id+"").innerHTML = questionHTML
}

function resetTestAnswers(ansCount){
    showTest(temp,true,ansCount)
}
function textResponseHandler(answer,showTyping,preChat){
    const parsedContent = answer.answer;
    let botMessageHTML = ""
    isTyping.style.display='none'
    const hisGptLogId = answer.gptLogId
    const hisfeedbackType = answer.feedbackType
    const botAnswerWrapper = document.createElement('div')
    botAnswerWrapper.id = "ans_"+answersCount
    botAnswerWrapper.classList.add('botAnswerWrapper')

    botMessageHTML +="<div class='botTutorIcon'>" +
        "<img src='/assets/resource/tutor-icon.svg'/>" +
        "</div>"+
        "<div style='width: 100%;'>" +
        "<div class='message botMessage' id='ansDiv_"+answersCount+"'>" +
        "<div id='mesPlaceholder_"+answersCount+"' style='padding: 16px;word-break: break-word'></div>" +
        "</div>" +
        "<div class='feedbackWrap' id='feedbackWrap_"+answersCount+"' style='display:none;'>" +
        "<div style='display: flex;align-items: center;gap: 10px'>";
        if(showReadOption){
            botMessageHTML += "<span class='readOtp' id='readOtp_"+answersCount+"'  onclick=readResponse('"+answersCount+"')>" +
                "<i class='fa-regular fa-circle-play'></i>" +
                "</span>";
        }
    if(hisfeedbackType!=null && hisfeedbackType=='like'){
        botMessageHTML += "<span><i class='fa-solid fa-thumbs-up' id='feedback_like_"+answersCount+"' disabled='true' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }else if(hisfeedbackType!=null && hisfeedbackType=="dislike"){
        botMessageHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-solid fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' disabled='true' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }else if(hisfeedbackType==null){
        botMessageHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }
    if(gptSiteId=="71"){
        botMessageHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"' style='display: none'  onclick=printContent('"+answersCount+"')>";
    }else{
        botMessageHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"'  onclick=printContent('"+answersCount+"')>";
    }
    botMessageHTML +="<i class='fa-solid fa-print'></i>" +
        "<span class='printToolTip' >Print chat</span>"+
        "</span>";
    botMessageHTML +="</div>" +
        "<div id='feedbackInput_"+answersCount+"'></div>"+
        "</div>" +
        "</div>"+
        "</div>";
    botAnswerWrapper.innerHTML = botMessageHTML
    if(preChat){
        messages.prepend(botAnswerWrapper)
    }else{
        messages.append(botAnswerWrapper)
    }
    var currId = "mesPlaceholder_"+answersCount+""
    if(showTyping){
        conversation.scrollTop = conversation.scrollHeight + 200
        document.getElementById('sendBtn').innerHTML = '<i class="fa-solid fa-pause"></i>'
        typeWriter(parsedContent, 0);
    }else{
        const ansDiv = document.getElementById("mesPlaceholder_"+answersCount+"")
        ansDiv.innerHTML = parsedContent
        document.querySelectorAll('pre code').forEach((block) => {
            hljs.highlightBlock(block);
        });
        renderMathInElement(ansDiv, {
            delimiters: [
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true }
            ]
        });
        ansDiv.innerHTML = marked.parse(ansDiv.innerHTML)
        document.querySelectorAll('pre code').forEach((block) => {
            hljs.highlightBlock(block);
        });
        document.getElementById("feedbackWrap_"+answersCount+"").style.display='block'
    }

    function typeWriter(text, i, callback) {
        isResponseComplete = false;
        var answerDiv = document.getElementById(currId);
        typingWorker = new Worker('/assets/bookGPTScripts/typeWorker.js');

        typingWorker.postMessage({ text: text, index: i, speed: 10 });

        typingWorker.onmessage = function (e) {
            if (e.data.done) {
                typingWorker.terminate();
                typingWorker = null;

                const tempEl = document.createElement("div");
                tempEl.innerHTML = text;
                renderMathInElement(tempEl, {
                    delimiters: [
                        { left: "\\(", right: "\\)", display: false },
                        { left: "\\[", right: "\\]", display: true }
                    ]
                });
                const renderedHtml = marked.parse(tempEl.innerHTML);
                answerDiv.innerHTML = renderedHtml;
                conversation.scrollTop = conversation.scrollHeight;
                document.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightBlock(block);
                });
                document.getElementById("feedbackWrap_" + answersCount).style.display = 'block';
                document.getElementById('sendBtn').innerHTML = '<i class="fa-solid fa-paper-plane"></i>';
                isResponseComplete = true;

                if (callback) {
                    callback();
                }
            } else {
                const tempEl = document.createElement("div");
                tempEl.innerHTML = text.substring(0, e.data.index);
                renderMathInElement(tempEl, {
                    delimiters: [
                        { left: "\\(", right: "\\)", display: false },
                        { left: "\\[", right: "\\]", display: true }
                    ]
                });
                answerDiv.innerHTML = marked.parse(tempEl.innerHTML);
                document.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightBlock(block);
                });
            }
        };
    }
}

function mcqAnswerHandler(answer){
    let mcqOptsHTML = ""
    isTyping.style.display='none'
    const parsedContent = JSON.parse(answer.mcqs);

    let gptQuizResId=0
    let gptQuizId=0

    if(answer.resId){
        gptQuizResId = answer.resId
    }
    if(answer.quizId){
        gptQuizId = answer.quizId
    }
    const id = answersCount
    const hisGptLogId = answer.gptLogId
    const hisfeedbackType = answer.feedbackType

    const questions = parsedContent
    const noOfQuestions = temp.noOfQuestions
    const difficultyLevel = temp.difficultyLevel
    let questionHTML = ''
    let qCount = 0
    for(var q=0;q<questions.length;q++){
        qCount++
        questionHTML+= "<p>Q"+qCount+". "+questions[q].ps+"</p>"
        questionHTML+= "<p>A. "+questions[q].op1+"</p>"
        questionHTML+= "<p>B. "+questions[q].op2+"</p>"
        questionHTML+= "<p>C. "+questions[q].op3+"</p>"
        questionHTML+= "<p>D. "+questions[q].op4+"</p>"
        questionHTML+= "<br/>"

        if(questions[q].ans1 && questions[q].ans1 === "Yes"){
            questionHTML+= "<p>Answer: "+questions[q].op1+"</p>"
        }else if(questions[q].ans2 && questions[q].ans2 === "Yes"){
            questionHTML+= "<p>Answer: "+questions[q].op2+"</p>"
        }else if(questions[q].ans3 && questions[q].ans3 === "Yes"){
            questionHTML+= "<p>Answer: "+questions[q].op3+"</p>"
        }else if(questions[q].ans4 && questions[q].ans4 === "Yes"){
            questionHTML+= "<p>Answer: "+questions[q].op4+"</p>"
        }

        questionHTML+= "<p>Difficulty: "+questions[q].difficultyLevel+"</p>"
        questionHTML+= "<p>Explanation: "+questions[q].answerDescription+"</p>"
        questionHTML+= "<br/>"
    }

    const tempEl = document.createElement("div")
    tempEl.innerHTML = questionHTML
    renderMathInElement(tempEl, {
        delimiters: [
            { left: "\\(", right: "\\)", display: false },
            { left: "\\[", right: "\\]", display: true }
        ]
    });

    questionHTML = marked.parse(tempEl.innerHTML);

    const botAnswerWrapper = document.createElement('div')
    botAnswerWrapper.id = "ans_"+answersCount
    botAnswerWrapper.classList.add('botAnswerWrapper')

    mcqOptsHTML+= "<div class='botTutorIcon'>" +
        "<img src='/assets/resource/tutor-icon.svg'/>" +
        "</div>"+
        "<div style='width: 100%;'>" +
        "<div class='message botMessage'>" +
        "<p style='font-size: 16px;margin-bottom: 12px;font-weight: 500;'>You're all set! Dive into your MCQs.</p>"+
        "<div style='margin-top: 12px;display: none' id='showMCQDiv_"+answersCount+"'>" +
        "<div style='padding: 16px;word-break: break-word'>" + questionHTML + "</div>" +
        "</div>" +
        "<div class='nonText_options' style='margin-top:12px;'>" +
        "<button class='nonText_options_btn' data-type='show' id='showMCQBtn_"+answersCount+"' data-id="+answersCount+" onclick=mcqListener(event,'" + gptQuizResId + "','" + gptQuizId + "','"+id+"','"+noOfQuestions+"','"+difficultyLevel+"')>" +
        "<span data-type='show'>Show MCQs</span>" +
        "</button>" +
        "<button class='nonText_options_btn' data-type='play' data-id="+answersCount+" onclick=mcqListener(event,'" + gptQuizResId + "','" + gptQuizId + "','"+id+"','"+noOfQuestions+"','"+difficultyLevel+"')>" +
        "<span data-type='play'>Play with AI</span>" +
        "</button>" +
        "<button class='nonText_options_btn' data-type='practice' data-id="+answersCount+" onclick=mcqListener(event,'" + gptQuizResId + "','" + gptQuizId + "','"+id+"','"+noOfQuestions+"','"+difficultyLevel+"')>" +
        "<span data-type='practice'>Practice</span>" +
        "</button>" +
        "<button class='nonText_options_btn' data-type='test' data-id="+answersCount+" onclick=mcqListener(event,'" + gptQuizResId + "','" + gptQuizId + "','"+id+"','"+noOfQuestions+"','"+difficultyLevel+"')>" +
        "<span data-type='test'>Test</span>" +
        "</button>" +
        "</div>"+
        "</div>" +
        "<div class='feedbackWrap' id='feedbackWrap_"+answersCount+"' style='display: block;'>" +
        "<div style='display: flex;align-items: center;gap: 10px'>";
        if(showReadOption){
            mcqOptsHTML += "<span class='readOtp' id='readOtp_"+answersCount+"'  onclick=readResponse('"+answersCount+"')>" +
                "<i class='fa-regular fa-circle-play'></i>" +
                "</span>";
        }
    if(hisfeedbackType!=null && hisfeedbackType=='like'){
        mcqOptsHTML += "<span><i class='fa-solid fa-thumbs-up' id='feedback_like_"+answersCount+"' disabled='true' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }else if(hisfeedbackType!=null && hisfeedbackType=="dislike"){
        mcqOptsHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-solid fa-thumbs-down' id='feedback_dislike_"+answersCount+"' disabled='true' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }else if(hisfeedbackType==null){
        mcqOptsHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }
    if(gptSiteId=="71"){
        mcqOptsHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"' style='display: none'  onclick=printContent('"+answersCount+"')>";
    }else{
        mcqOptsHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"'  onclick=printContent('"+answersCount+"')>";
    }
    mcqOptsHTML +="<i class='fa-solid fa-print'></i>" +
        "<span class='printToolTip' >Print chat</span>"+
        "</span>";
    mcqOptsHTML +="</div>" +
        "<div id='feedbackInput_"+answersCount+"' class='customfeedbackInput'></div>"+
        "</div>" +
        "</div>" +
        "</div>";
    botAnswerWrapper.innerHTML = mcqOptsHTML
    messages.append(botAnswerWrapper)
    conversation.scrollTop = conversation.scrollHeight
    history_for_llm.push({
        user: document.getElementById("userQue_"+id).textContent,
        ai: document.getElementById("showMCQDiv_"+id).textContent
    });
    isResponseComplete = true
}

function mcqListener(event,gptQuizResId,gptQuizId,id,noOfQuestions,difficultyLevel){
        console.log("event in mcqListener",event.target.getAttribute('data-type'))
    const type = event.target.getAttribute('data-type');
    if(type === 'show') {
        document.getElementById("showMCQDiv_"+id).style.display='block'
        document.getElementById("showMCQBtn_"+id).style.display='none'
    } else if(type === 'play') {
        const url = "/prepjoy/prepJoyGame?quizId=" + gptQuizId + "&resId=" + gptQuizResId + "&quizType=&source=web&siteName="+siteName+"&learn=false&pubDesk=false&dailyTest=false&noOfQuestions="+noOfQuestions+"&difficultyLevel="+difficultyLevel+"&fromgpt=true";
        window.open(url, '_blank');
    }else if(type === 'practice'){
        const url = "/prepjoy/prepJoyGame?quizId=" + gptQuizId + "&resId=" + gptQuizResId + "&quizType=practice&source=web&siteName="+siteName+"&learn=false&pubDesk=false&dailyTest=false&noOfQuestions="+noOfQuestions+"&difficultyLevel="+difficultyLevel+"&fromgpt=true";
        window.open(url, '_blank');
    }else if(type === 'test'){
        const url = "/prepjoy/prepJoyGame?quizId=" + gptQuizId + "&resId=" + gptQuizResId + "&quizType=testSeries&source=web&siteName="+siteName+"&learn=false&pubDesk=false&dailyTest=false&noOfQuestions="+noOfQuestions+"&difficultyLevel="+difficultyLevel+"&fromgpt=true";
        window.open(url, '_blank');
    }
}

function videoResponseHandler(videosList){
    let videocardHTML = ""
        if(videosList.length>0){
            lastPlayed = ''
            if (player) {
                player.destroy()
            }
            videocardHTML = createVideoUI(videosList);
            let mcqOptsHTML = ""
            const hisGptLogId = null
            const hisfeedbackType = null
            isTyping.style.display='none'

            const botAnswerWrapper = document.createElement('div')
            botAnswerWrapper.id = "ans_"+answersCount
            botAnswerWrapper.classList.add('botAnswerWrapper')

            mcqOptsHTML+=   "<div class='botTutorIcon'>" +
                                "<img src='/assets/resource/tutor-icon.svg'/>" +
                            "</div>"+
                            "<div style='width: 100%;'>" +
                                "<div class='message botMessage' style='background: #fff !important'>" +
                                    videocardHTML+
                                "</div>"+
                                "<div class='feedbackWrap' id='feedbackWrap_"+answersCount+"'>" +
                                    "<div style='display: flex;align-items: center;gap: 10px'>";
                                    if(hisfeedbackType!=null && hisfeedbackType=='like'){
                                        mcqOptsHTML += "<span><i class='fa-solid fa-thumbs-up' id='feedback_like_"+answersCount+"' disabled='true'  data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
                                            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
                                    }else if(hisfeedbackType!=null && hisfeedbackType=="dislike"){
                                        mcqOptsHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
                                            "<span><i class='fa-solid fa-thumbs-down' id='feedback_dislike_"+answersCount+"' disabled='true' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
                                    }else if(hisfeedbackType==null){
                                        mcqOptsHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
                                            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
                                    }
                                mcqOptsHTML +="</div>" +
                                "</div>" +
                            "</div>";
            botAnswerWrapper.innerHTML = mcqOptsHTML
            messages.append(botAnswerWrapper)
            handleVideoModal(answersCount)
        }else{
            //display the message that videos are not yet added. Will be added shortly.
            let mcqOptsHTML = ""
            const hisGptLogId = null
            const hisfeedbackType = null
            isTyping.style.display='none'

            const botAnswerWrapper = document.createElement('div')
            botAnswerWrapper.id = "ans_"+answersCount
            botAnswerWrapper.classList.add('botAnswerWrapper')
            mcqOptsHTML+=   "<div class='botTutorIcon'>" +
                "<img src='/assets/resource/tutor-icon.svg'/>" +
                "</div>"+
                "<div style='width: 100%;'>" +
                "<div class='message botMessage' style='background: #fff !important'> No videos available. Will be added shortly" +
                "</div>";
            mcqOptsHTML +="</div>";
            botAnswerWrapper.innerHTML = mcqOptsHTML
            messages.append(botAnswerWrapper)
        }
    isTyping.style.display='none'
    conversation.scrollTop = conversation.scrollHeight
    isResponseComplete = true

}

function createVideoUI(list){
    const videos_array = list

    let videosHTML = '<div class="videos">';

    videos_array.forEach((video, index) => {
        videoIndex++
        videosHTML += '<div id="videoThumbnail' + videoIndex + '" class="thumbnail-container" data-id="'+video.id+'">';
        videosHTML += '<div class="thumbnail-wrapper">';
        videosHTML += '<img class="thumbnail" src="https://img.youtube.com/vi/' + video.videoId + '/maxresdefault.jpg" alt="Video thumbnail">';
        videosHTML += '<div class="thumbnail-overlay"></div>';
        videosHTML += '<div class="play-overlay">';
        videosHTML += '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="white">';
        videosHTML += '<path d="M8 5v14l11-7z"></path>';
        videosHTML += '</svg>';
        videosHTML += '</div>'; // Close play-overlay
        videosHTML += '</div>'; // Close thumbnail-wrapper
        videosHTML += '</div>'; // Close thumbnail-container
    });

    videosHTML += '</div>'; // Close videos
    return videosHTML
}

function handleVideoModal(id){
    let progressInterval
    const modal = document.getElementById('videoModal')
    const closeButton = document.getElementById('closeModal')
    const thumbnailContainer = document.querySelectorAll('.thumbnail-container')

    closeButton.addEventListener('click', () => {
        modal.classList.remove('active')
        if (player) {
            player.pauseVideo()
        }
    })

    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modal.classList.contains('active')) {
            modal.classList.remove('active')
            if (player) {
                player.pauseVideo()
            }
        }
    })


    thumbnailContainer.forEach((container, index) => {
        container.addEventListener('click', () => {
            const dataId = container.getAttribute('data-id')
            const videoObj = suggestedVideos.find(video => video.id == dataId)
            const videoId = videoObj.videoId
            modal.classList.add('active')

            if (lastPlayed !== videoId) {
                lastPlayed = videoId

                if (player) {
                    player.destroy()
                }

                initializePlayer(videoId)
            } else {
                player.playVideo()
            }
        })
    })

    function togglePlayPause() {
        const playPauseBtn = document.querySelector('.play-pause svg path')
        if (player.getPlayerState() === YT.PlayerState.PLAYING) {
            player.pauseVideo()
            playPauseBtn.setAttribute('d', 'M8 5v14l11-7z') // Play icon
        } else {
            player.playVideo()
            playPauseBtn.setAttribute('d', 'M6 19h4V5H6v14zm6-14v14h4V5h-4z') // Pause icon
        }
    }

    function startProgressTracker() {
        const progress = document.querySelector('.progress')
        const timeDisplay = document.querySelector('.time-display')

        clearInterval(progressInterval)
        progressInterval = setInterval(() => {
            const currentTime = player.getCurrentTime()
            const duration = player.getDuration()
            const percentage = (currentTime / duration) * 100

            progress.style.width = percentage + '%'
            timeDisplay.textContent =
                formatTime(currentTime) + ' / ' + formatTime(duration)
        }, 500)
    }

    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60)
        const secs = Math.floor(seconds % 60)
            .toString()
            .padStart(2, '0')
        return `${minutes}:${secs}`
    }


    document.querySelector('.progress-bar').addEventListener('click', (e) => {
        const progressBar = e.currentTarget
        const offsetX = e.offsetX
        const newTime =
            (offsetX / progressBar.offsetWidth) * player.getDuration()
        player.seekTo(newTime, true)
    })

    function initializePlayer(link) {
        player = new YT.Player('youtube-player', {
            height: '100%',
            width: '100%',
            videoId: link,
            playerVars: {
                autoplay: 1,
                controls: 0,
                enablejsapi: 1,
                modestbranding: 1,
                rel: 0,
                showinfo: 0,
            },
            events: {
                onReady: onPlayerReady,
                onStateChange: onPlayerStateChange,
            },
        })
    }

    function onPlayerReady(event) {
        setupControls()
        startProgressTracker()
        if(isMute){
            player.mute()
            volumeBtn.innerHTML = `
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
          </svg>
        `;
        }
    }

    function setupControls() {
        const playPauseBtn = document.querySelector('.play-pause')
        const volumeBtn = document.querySelector('.volume')
        const progressBar = document.querySelector('.progress-bar')

        playPauseBtn.addEventListener('click', togglePlayPause)
        volumeBtn.addEventListener('click', toggleMute)
        progressBar.addEventListener('click', (e) => {
            const rect = progressBar.getBoundingClientRect()
            const pos = (e.clientX - rect.left) / rect.width
            const duration = player.getDuration()
            player.seekTo(duration * pos, true)
        })
    }

    function toggleMute() {
        const volumeBtn = document.querySelector('.volume')
        if (player.isMuted()) {
            player.unMute()
            volumeBtn.innerHTML = `
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z"/>
          </svg>
        `
         isMute = false;
        } else {
            player.mute()
            volumeBtn.innerHTML = `
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
          </svg>
        `;
        isMute = true;
        }
    }

    function onPlayerStateChange(event) {
        const playPauseBtn = document.querySelector('.play-pause')
        if (event.data === YT.PlayerState.PLAYING) {
            playPauseBtn.innerHTML = `
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
          </svg>
        `
        } else {
            playPauseBtn.innerHTML = `
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 5v14l11-7z"/>
          </svg>
        `
        }
    }
}

function generateVideoCardHTML(video) {
    var videoCardHTML = "<div class='video-card'>";
    videoCardHTML += "<img class='thumbnail' src='https://img.youtube.com/vi/" + video.videoId + "/hqdefault.jpg' alt='" + video.videoTitle + "'>";
    videoCardHTML += "<button class='view-btn' data-video-id='" + video.videoId + "' onclick='openytVideo(\"" + video.videoId + "\")'>View Video</button>";
    videoCardHTML += "</div>";
    return videoCardHTML;
}
function openytVideo(videoId){
    const videoModal = document.getElementById("videoModal");
    const youtubeVideo = document.getElementById("youtubeVideo");
    videoModal.style.display = "block";
    youtubeVideo.src = "https://www.youtube.com/embed/" + videoId + "?autoplay=1";
}
function closeytMdal(){
    const videoModal = document.getElementById("videoModal");
    const youtubeVideo = document.getElementById("youtubeVideo");
    videoModal.style.display = "none";
    youtubeVideo.src = "";  // Stop the video
}
function flashcardResponseHandler(answer){
    let mcqOptsHTML = ""
    isTyping.style.display='none'
    const parsedContent = answer.flashcards;
    let gptFcResId = 0
    let resourceName =''

    if(answer.resId){
        gptFcResId = answer.resId
    }
    const noOfQuestions = temp.noOfQuestions
    if(answer.resourceName){
        resourceName = answer.resourceName
    }
    const id = answersCount
    const hisGptLogId = answer.gptLogId
    const hisfeedbackType = answer.feedbackType

    let questionHTML = ''
    let qCount = 0

    for(var q=0;q<parsedContent.length;q++){
        qCount++
        questionHTML+= "<p>Front: "+parsedContent[q].term+"</p>"
        questionHTML+= "<p>Back: "+parsedContent[q].definition+"</p>"
        questionHTML+= "<br/>"
        if(q!=parsedContent.length-1){
            questionHTML+= "<div style='width:90%;border-radius: 10px;border-bottom: 1px dashed #444'></div>"
        }
        questionHTML+= "\n"
    }

    const tempEl = document.createElement("div")
    tempEl.innerHTML = questionHTML
    renderMathInElement(tempEl, {
        delimiters: [
            { left: "\\(", right: "\\)", display: false },
            { left: "\\[", right: "\\]", display: true }
        ]
    });
    questionHTML = marked.parse(tempEl.innerHTML);
    const fcId= answersCount

    const botAnswerWrapper = document.createElement('div')
    botAnswerWrapper.id = "ans_"+answersCount
    botAnswerWrapper.classList.add('botAnswerWrapper')

    mcqOptsHTML+= "<div class='botTutorIcon'>" +
        "<img src='/assets/resource/tutor-icon.svg'/>" +
        "</div>"+
        "<div style='width: 100%;'>" +
        "<div class='message botMessage'>" +
        "<p style='font-size: 16px;margin-bottom: 12px;font-weight: 500;'>You're all set! Dive into your Flashcards.</p>"+
        "<div style='margin-top: 12px;display: none' id='showFlashcardDiv_"+answersCount+"'><div style='padding: 16px;word-break: break-word'>" + questionHTML + "</div></div>" +
        "<div class='nonText_options' style='margin-top:12px;'>" +
        "<button class='nonText_options_btn' data-type='show' id='showFCBtn_"+answersCount+"' data-id="+answersCount+" onclick=\"flashcardListener(event,'" + gptFcResId + "','" + resourceName + "','"+id+"','"+noOfQuestions+"')\">" +
        "<span data-type='show'>Show Flashcards</span>" +
        "</button>" +
        "<button class='nonText_options_btn' data-type='open' data-id="+answersCount+" onclick=\"flashcardListener(event,'" + gptFcResId + "','" + resourceName + "','"+id+"','"+noOfQuestions+"')\">" +
        "<span data-type='open'>Open Flashcards</span>" +
        "</button>" +
        "</div>"+
        "</div>" +
        "<div class='feedbackWrap' id='feedbackWrap_"+answersCount+"'>" +
        "<div style='display: flex;align-items: center;gap: 10px'>";
        if(showReadOption){
            mcqOptsHTML += "<span class='readOtp' id='readOtp_"+answersCount+"'  onclick=readResponse('"+answersCount+"')>" +
                "<i class='fa-regular fa-circle-play'></i>" +
                "</span>";
        }
    if(hisfeedbackType!=null && hisfeedbackType=='like'){
        mcqOptsHTML += "<span><i class='fa-solid fa-thumbs-up' id='feedback_like_"+answersCount+"' disabled='true' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }else if(hisfeedbackType!=null && hisfeedbackType=="dislike"){
        mcqOptsHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-solid fa-thumbs-down' id='feedback_dislike_"+answersCount+"' disabled='true' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }else if(hisfeedbackType==null){
        mcqOptsHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }
    if(gptSiteId=="71"){
        mcqOptsHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"' style='display: none'  onclick=printContent('"+answersCount+"')>";
    }else{
        mcqOptsHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"'  onclick=printContent('"+answersCount+"')>";
    }
    mcqOptsHTML +="<i class='fa-solid fa-print'></i>" +
        "<span class='printToolTip' >Print chat</span>"+
        "</span>";
    mcqOptsHTML+= "</div>" +
        "<div id='feedbackInput_"+answersCount+"'></div>"+
        "</div>" +
        "</div>";
    botAnswerWrapper.innerHTML = mcqOptsHTML
    messages.append(botAnswerWrapper)
    conversation.scrollTop = conversation.scrollHeight
    history_for_llm.push({
        user: document.getElementById("userQue_"+fcId).textContent,
        ai: document.getElementById("showFlashcardDiv_"+fcId).textContent
    });
    isResponseComplete = true
}

function flashcardListener(event,gptFcResId,resourceName,id,noOfQuestions){
    const type = event.target.getAttribute('data-type');
    if(type === 'show') {
        document.getElementById("showFlashcardDiv_"+id+"").style.display='block'
        document.getElementById("showFCBtn_"+id+"").style.display='none';
    } else if(type === 'open') {
        let url=""
        if(noOfQuestions!="" && noOfQuestions!=null && noOfQuestions!=undefined){
            url = "/resources/displayFlashCards?resId=" + gptFcResId+ "&name=" +resourceName+"&noOfQuestions="+noOfQuestions
        }else{
            url = "/resources/displayFlashCards?resId=" + gptFcResId+ "&name=" +resourceName
        }

        window.open(url, '_blank');
    }
}

function qnaResponseHandler(answer){
    let mcqOptsHTML = ""
    isTyping.style.display='none'
    const parsedContent = answer.qaList;

    const id = answersCount
    const hisGptLogId = answer.gptLogId
    const hisfeedbackType = answer.feedbackType

    let questionHTML = ''
    let qCount = 0
    const questions = parsedContent
    for(var q=0;q<questions.length;q++){
        qCount++
        questionHTML+= "<p>Q"+qCount+". "+questions[q].question+"</p>"
        questionHTML+= "<p>Answer: "+questions[q].answer+"</p>"
        questionHTML+= "<p>Difficulty: "+questions[q].difficultyLevel+"</p>"
        questionHTML+= "<br/>"
    }
    const tempEl = document.createElement("div")
    tempEl.innerHTML = questionHTML
    renderMathInElement(tempEl, {
        delimiters: [
            { left: "\\(", right: "\\)", display: false },
            { left: "\\[", right: "\\]", display: true }
        ]
    });
    questionHTML = marked.parse( tempEl.innerHTML);

    const botAnswerWrapper = document.createElement('div')
    botAnswerWrapper.id = "ans_"+answersCount
    botAnswerWrapper.classList.add('botAnswerWrapper')

    mcqOptsHTML+= "<div class='botTutorIcon'>" +
        "<img src='/assets/resource/tutor-icon.svg'/>" +
        "</div>"+
        "<div style='width: 100%;'>" +
        "<div class='message botMessage'>" +
        "<p style='font-size: 16px;margin-bottom: 12px;font-weight: 500;'>You're all set! Dive into your Question and Answers.</p>"+
        "<div style='margin-top: 12px;display: none' id='showQnADiv_"+answersCount+"'><div style='padding: 16px;word-break: break-word'>" + questionHTML + "</div></div>" +
        "<div class='nonText_options' style='margin-top:12px;'>" +
        "<button class='nonText_options_btn' style='width: auto !important;' id='showQABtn_"+answersCount+"' data-type='show' data-id="+answersCount+" onclick=\"qandaListener(event,'"+id+"')\">" +
        "<span data-type='show'>Show Question & Answers</span>" +
        "</button>" +
        "</div>"+
        "</div>" +
        "<div class='feedbackWrap' id='feedbackWrap_"+answersCount+"'>" +
        "<div style='display: flex;align-items: center;gap: 10px'>";
        if(showReadOption){
            mcqOptsHTML += "<span class='readOtp' id='readOtp_"+answersCount+"'  onclick=readResponse('"+answersCount+"')>" +
                "<i class='fa-regular fa-circle-play'></i>" +
                "</span>";
        }
    if(hisfeedbackType!=null && hisfeedbackType=='like'){
        mcqOptsHTML += "<span><i class='fa-solid fa-thumbs-up' id='feedback_like_"+answersCount+"' disabled='true' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }else if(hisfeedbackType!=null && hisfeedbackType=="dislike"){
        mcqOptsHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-solid fa-thumbs-down' id='feedback_dislike_"+answersCount+"' disabled='true' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }else if(hisfeedbackType==null){
        mcqOptsHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }
    if(gptSiteId=="71"){
        mcqOptsHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"' style='display: none'  onclick=printContent('"+answersCount+"')>";
    }else{
        mcqOptsHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"'  onclick=printContent('"+answersCount+"')>";
    }
    mcqOptsHTML +="<i class='fa-solid fa-print'></i>" +
        "<span class='printToolTip' >Print chat</span>"+
        "</span>";
    mcqOptsHTML+= "</div>" +
        "<div id='feedbackInput_"+answersCount+"'></div>"+
        "</div>" +
        "</div>";
    botAnswerWrapper.innerHTML = mcqOptsHTML
    messages.append(botAnswerWrapper)
    conversation.scrollTop = conversation.scrollHeight
    history_for_llm.push({
        user: document.getElementById("userQue_"+id).textContent,
        ai: document.getElementById("showQnADiv_"+id).textContent
    });
    isResponseComplete = true
}

function pyqsResponseHandler(answer){
    let mcqOptsHTML = ""
    isTyping.style.display='none'
    const parsedContent = answer.qaList;

    const id = answersCount
    const hisGptLogId = answer.gptLogId
    const hisfeedbackType = answer.feedbackType

    let questionHTML = ''
    let qCount = 0
    const questions = parsedContent
    for(var q=0;q<questions.length;q++){
        qCount++
        questionHTML+= "<p>Q"+qCount+". "+questions[q].question+"</p>"

        // Handle explainLink formatting
        if(questions[q].explainLink){
            let explainParts = questions[q].explainLink.split(',');
            let university = explainParts[0] ? explainParts[0].trim() : '';
            let year = explainParts[1] ? explainParts[1].trim() : '';

            let explainText = '';
            if(university && university !== 'Unknown' && year && year !== 'Unknown'){
                explainText = university + ', ' + year;
            } else if(university && university !== 'Unknown'){
                explainText = university;
            } else if(year && year !== 'Unknown'){
                explainText = year;
            }

            if(explainText){
                questionHTML+= "<p style='text-align: right; color: #666; margin-top: 5px;'>(" + explainText + ")</p>"
            }
        }

        questionHTML+= "<p>Answer: "+questions[q].answer+"</p>"
        questionHTML+= "<p>Difficulty: "+questions[q].difficultyLevel+"</p>"
        questionHTML+= "<br/>"
    }
    const tempEl = document.createElement("div")
    tempEl.innerHTML = questionHTML
    renderMathInElement(tempEl, {
        delimiters: [
            { left: "\\(", right: "\\)", display: false },
            { left: "\\[", right: "\\]", display: true }
        ]
    });
    questionHTML = marked.parse( tempEl.innerHTML);

    const botAnswerWrapper = document.createElement('div')
    botAnswerWrapper.id = "ans_"+answersCount
    botAnswerWrapper.classList.add('botAnswerWrapper')

    mcqOptsHTML+= "<div class='botTutorIcon'>" +
        "<img src='/assets/resource/tutor-icon.svg'/>" +
        "</div>"+
        "<div style='width: 100%;'>" +
        "<div class='message botMessage'>" +
        "<p style='font-size: 16px;margin-bottom: 12px;font-weight: 500;'>You're all set! Dive into your Previous Year Questions.</p>"+
        "<div style='margin-top: 12px;display: none' id='showPyqsDiv_"+answersCount+"'><div style='padding: 16px;word-break: break-word'>" + questionHTML + "</div></div>" +
        "<div class='nonText_options' style='margin-top:12px;'>" +
        "<button class='nonText_options_btn' style='width: auto !important;' id='showPyqsBtn_"+answersCount+"' data-type='show' data-id="+answersCount+" onclick=\"pyqsListener(event,'"+id+"')\">" +
        "<span data-type='show'>Show Previous Year Questions</span>" +
        "</button>" +
        "</div>"+
        "</div>" +
        "<div class='feedbackWrap' id='feedbackWrap_"+answersCount+"'>" +
        "<div style='display: flex;align-items: center;gap: 10px'>";
        if(showReadOption){
            mcqOptsHTML += "<span class='readOtp' id='readOtp_"+answersCount+"'  onclick=readResponse('"+answersCount+"')>" +
                "<i class='fa-regular fa-circle-play'></i>" +
                "</span>";
        }
    if(hisfeedbackType!=null && hisfeedbackType=='like'){
        mcqOptsHTML += "<span><i class='fa-solid fa-thumbs-up' id='feedback_like_"+answersCount+"' disabled='true' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }else if(hisfeedbackType!=null && hisfeedbackType=="dislike"){
        mcqOptsHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-solid fa-thumbs-down' id='feedback_dislike_"+answersCount+"' disabled='true' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }else if(hisfeedbackType==null){
        mcqOptsHTML += "<span><i class='fa-regular fa-thumbs-up' id='feedback_like_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=likeResHandler(event,'"+answersCount+"')></i></span>"+
            "<span><i class='fa-regular fa-thumbs-down' id='feedback_dislike_"+answersCount+"' data-gptLogId='"+hisGptLogId+"' data-id="+answersCount+" onclick=dislikeResHandler(event,'"+answersCount+"')></i></span>";
    }
    if(gptSiteId=="71"){
        mcqOptsHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"' style='display: none'  onclick=printContent('"+answersCount+"')>";
    }else{
        mcqOptsHTML += "<span class='printOpt' id='printToolTip_"+answersCount+"'  onclick=printContent('"+answersCount+"')>";
    }
    mcqOptsHTML +="<i class='fa-solid fa-print'></i>" +
        "<span class='printToolTip' >Print chat</span>"+
        "</span>";
    mcqOptsHTML+= "</div>" +
        "<div id='feedbackInput_"+answersCount+"'></div>"+
        "</div>" +
        "</div>";
    botAnswerWrapper.innerHTML = mcqOptsHTML
    messages.append(botAnswerWrapper)
    conversation.scrollTop = conversation.scrollHeight
    history_for_llm.push({
        user: document.getElementById("userQue_"+id).textContent,
        ai: document.getElementById("showPyqsDiv_"+id).textContent
    });
    isResponseComplete = true
}

function qandaListener(event,id){
    const type = event.target.getAttribute('data-type');
    if(type === 'show') {
        document.getElementById("showQnADiv_"+id+"").style.display='block'
        document.getElementById("showQABtn_"+id+"").style.display='none'
    }
}

function pyqsListener(event,id){
    const type = event.target.getAttribute('data-type');
    if(type === 'show') {
        document.getElementById("showPyqsDiv_"+id+"").style.display='block'
        document.getElementById("showPyqsBtn_"+id+"").style.display='none'
    }
}

function likeResHandler(event,id){
    const feedback_like = document.getElementById("feedback_like_"+id+"")
    const feedback_dislike = document.getElementById("feedback_dislike_"+id+"")

    let isClicked = feedback_like.getAttribute('disabled')
    if(isClicked=='true' || isClicked==true){
        return
    }else{
        feedback_like.setAttribute('disabled','true')
        feedback_dislike.removeAttribute('disabled')
    }
    const gptLogtempId = feedback_like.getAttribute('data-gptLogId')
    gptLogId = Number(gptLogtempId)
    feedbackType = 'like'

    feedback_like.classList.toggle('fa-regular')
    feedback_like.classList.toggle('fa-solid')

    if(feedback_dislike.classList.contains('fa-solid')){
        feedback_dislike.classList.remove('fa-solid')
        feedback_dislike.classList.add('fa-regular')
        closefeedback(id)
    }
    closefeedback(id)
}
function dislikeResHandler(event,id){
    const feedback_dislike = document.getElementById("feedback_dislike_"+id+"")
    const feedback_like = document.getElementById("feedback_like_"+id+"")
    const feedbackInput = document.getElementById("feedbackInput_"+id+"")

    let isClicked = feedback_dislike.getAttribute('disabled')
    if(isClicked=='true' || isClicked==true){
        return
    }else{
        feedback_dislike.setAttribute('disabled','true')
        feedback_like.removeAttribute('disabled')
    }
    feedback_dislike.classList.toggle('fa-regular')
    feedback_dislike.classList.toggle('fa-solid')

    const gptLogtempId = feedback_dislike.getAttribute('data-gptLogId')
    gptLogId = Number(gptLogtempId)
    feedbackType = 'dislike'

    let clicked = feedback_like.classList.contains('fa-solid')
    if(clicked){
        feedback_like.classList.remove('fa-solid')
        feedback_like.classList.add('fa-regular')
    }

    let inputHTML = "<div class='feedbackOptions'>" +
        "<div class='closeBtn'>" +
        "<span onclick='closefeedback("+id+")'><i class='fa-solid fa-xmark'></i></span>"+
        "</div>"+
        "<div style='padding: 20px;border: 1px solid rgba(0, 0, 0, 0.2);border-radius: 10px;'>"+
        "<p style='margin-bottom: 30px;font-size: 15px;'>Let's make it perfect together. What can be improved in this answer?</p>"+
        "<div class='feedbackOptions_cards'>" +
        "<div class='feedbackOptions_card' onclick=dislikeOption('incorrect','"+id+"')><span>Incorrect Response</span></div>" +
        "<div class='feedbackOptions_card' onclick=\"dislikeOption('irrelevant')\"><span>Response is irrelevant</span></div>" +
        "<div class='feedbackOptions_card' onclick=\"dislikeOption('unclear or confusing')\"><span>Response is unclear or confusing</span></div>" +
        "<div class='feedbackOptions_card' onclick=\"dislikeOption('did not address my intent')\"><span>Response did not address my intent</span></div>" +
        "<div class='feedbackOptions_card' onclick=\"dislikeOption('inappropriate content')\"><span>Response has inappropriate content</span></div>" +
        "<div class='feedbackOptions_card' onclick=\"showfbInp()\"><span>Other (please specify)</span></div>" +
        "</div>" +
        "<div style='display: none;' id='fb_inputwrap'>" +
        "<input type='text' placeholder='What can we do better? Your feedback helps!' id='fb_input_"+id+"'>" +
        "<button class='feedback_submitBtn' onclick=fbSubmit("+id+")>Submit</button>"+
        "</div>" +
        "</div>" +
        "</div>"
    feedbackContent.innerHTML = inputHTML
    openModal()
}

function showfbInp(){
    document.getElementById('fb_inputwrap').style.display = 'flex';
}
function closefeedback(fbId){
    updateFeedback()
    modalClose(fbId)
}

function dislikeOption(feedback,id){
    feedbackText = feedback
    feedbackType = 'dislike'
    updateFeedback()
    modalClose(id)
}

function fbSubmit(id){
    feedbackText = document.getElementById("fb_input_"+id+"").value
    updateFeedback()
    modalClose(id)
}
function modalClose(fbId){
    modal.classList.add('fade-out');
    setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('fade-out');
    }, 500);
    document.getElementById("feedbackInput_"+fbId+"").innerHTML =""
}
async function updateFeedback(){
    try {
        const feedbackUpdate= await fetch('/gptLog/updateGPTLog?gptId='+gptLogId+'&feedbackType='+feedbackType+'&feedback='+feedbackText)
        const res = await feedbackUpdate.json()
    }catch (err){
        alert("Something went wrong.")
        console.log(err.message)
    }
}

async function storeQueryAndResponse(query,answer,promptType,ansId, imgLink){
    const logUserQuery = {
        username:username,
        response:answer,
        resId:gptResId,
        readingMaterialResId:null,
        promptType:promptType
    }
    if(isSnipQuestion && currImgLink){
        logUserQuery.imgLink = currImgLink
    }
    if(promptType=="userInput"){
        logUserQuery.userPrompt = query
        logUserQuery.systemPrompt = null
        logUserQuery.readingMaterialResId = gptResId
    }else{
        logUserQuery.userPrompt = null
        logUserQuery.systemPrompt = systemPromptLabel
    }

    if(isTestSeries && currentMcqObjId){
        logUserQuery.quizObjId = currentMcqObjId
        logUserQuery.systemPrompt = inputMessageLabel
    }
    try{
        const storeUserQuery = await fetch('/gptLog/save ',{
            method:"POST",
            body:JSON.stringify(logUserQuery),
            headers:{
                "Content-Type":"application/json"
            }
        })
        const storedResponse = await storeUserQuery.json()
        const storedRes = storedResponse.GptLog
        const feedback_dislike = document.getElementById("feedback_dislike_"+ansId+"")
        const feedback_like = document.getElementById("feedback_like_"+ansId+"")
        feedback_dislike.setAttribute('data-gptLogId',storedRes.id)
        feedback_like.setAttribute('data-gptLogId',storedRes.id)

        if(hasBookAccess=="true" && showTokens=="true") {
            freeTokenCount = storedResponse.freeTokenCount
            paidTokenCount = storedResponse.paidTokenCount
            updateTokenCount()
        }else if(hasBookAccess=="false" && previewMode=="true"){
            freeTokenCount = storedResponse.freeTokenCount
        }

    }catch (err){
        console.log(err)
    }
}

async function getChatHistory(){
    try{
        let historyAPI = "/gptLog/find?username="+username+"&resId="+gptResId+"&pageNo="+pageNo
        isTestSeries ? historyAPI+="&quizObjId="+currentMcqObjId :""
        const chatHistory = await fetch(historyAPI)
        let chatHistoryJSON = await chatHistory.json()
        chatHistoryJSON = chatHistoryJSON.GptLogs
        document.getElementById("messages").innerHTML = "";
        answersCount=0
        cardClicked = false
        if(chatHistoryJSON.length>0){
            hideorShowClearChat("show")
            const historyObj = chatHistoryJSON.reverse();
            globalChatHistory = historyObj
            if(historyObj.length>0){
                for(let c=0;c<historyObj.length;c++){
                    let question
                    if(historyObj[c].systemPrompt!=null && historyObj[c].userPrompt==null){
                        question = historyObj[c].systemPrompt
                    }else if(historyObj[c].systemPrompt==null && historyObj[c].userPrompt!=null){
                        question = historyObj[c].userPrompt
                    }else if(historyObj[c].systemPrompt!=null && historyObj[c].userPrompt!=null){
                        question = historyObj[c].systemPrompt
                    }
                    historyObj[c].answer = historyObj[c].response
                    historyObj[c].resType = historyObj[c].promptType
                    historyObj[c].gptLogId = historyObj[c].id
                    updateHistoryUI(historyObj[c],question)
                }
            }
        }
    }catch (err){
        console.log(err)
    }
}

function updateHistoryUI(resObj,question){
    showUserMessage(question,true,resObj)
    conversation.scrollTop = conversation.scrollHeight
}
function auto_grow(element) {
    element.style.height = "5px";
    element.style.height = (element.scrollHeight) + "px";
}
function auto_shrink(element){
    element.style.height = "20px";
    element.value = ''
}

function openModal() {
    modal.style.display = 'block';
}

function clearChat(){
    isClearingChat = true;
    document.getElementById("messages").innerHTML = "";
    hideorShowClearChat("hide")
    showClearChatBtn()
}

function hideorShowClearChat(type){
    if(type=='show') {
        document.getElementById('clearChatBtn').style.transform = 'translateX(0)';
    }else if(type=='hide'){
        document.getElementById('clearChatBtn').style.transform = 'translateX(150px)';
    }
    showClearChatBtn()
}
function showClearChatBtn(){
    const clearChatBtn = document.getElementById('clearChatBtn')
    const showClearChatBtn = document.querySelector('#showClearChatBtn i')
    const clearChatBtnIcon = document.querySelector('#showClearChatBtn')
    if(clearChatBtn.style.transform == 'translateX(0px)'){
        clearChatBtn.style.transform = 'translateX(150px)';
        showClearChatBtn.classList.remove('fa-chevron-right')
        showClearChatBtn.classList.add('fa-chevron-left')
        clearChatBtnIcon.style.right = '0px';
    }else if(clearChatBtn.style.transform = 'translateX(150px)'){
        clearChatBtn.style.transform = 'translateX(0)';
        showClearChatBtn.classList.remove('fa-chevron-left')
        showClearChatBtn.classList.add('fa-chevron-right')
        clearChatBtnIcon.style.right = '110px';
    }
}

//MOBILE WEB
const openBookGPT = document.getElementById('openBookGPT')
const mobilePdfViewer = document.getElementById('mobilePdfViewer')
const mobileChatViewer = document.getElementById('mobileChatViewer')
const readBookBtn = document.getElementById('readBookBtn')
const clearChatMob = document.querySelector('.clearChat')

if(openBookGPT){
    openBookGPT.addEventListener('click',()=>{
        showMobileChat()
    })
}

if(readBookBtn){
    readBookBtn.addEventListener('click',()=>{
        hideMobileChat()
    })
}

function showMobileChat(){

    setTimeout(function (){
        // const divElement = document.querySelector('.prompts_dropdown')
        // const isOpen = divElement.classList.contains('open');
        // if(isOpen){
        //     togglePromptsDropdown()
        // }
    },4000)
    mobilePdfViewer ? mobilePdfViewer.style.transform = 'translateX(-150%)' : ''
    mobileChatViewer ? mobileChatViewer.style.transform = 'translateX(0)': ''
    clearChatMob ?  clearChatMob.style.display = 'block': ''
    if(document.querySelector('.chaptersList')){
        document.querySelector('.chaptersList').style.display = "none";
    }
    if(document.getElementById('readBookBtn')){
        document.getElementById('readBookBtn').style.display = "block";
    }

    if(document.getElementById('mobNotice')){
        document.getElementById('mobNotice').style.display = 'none'
    }
}
function hideMobileChat(){

    mobilePdfViewer ? mobilePdfViewer.style.transform = 'translateX(0)' : ''
    mobileChatViewer ? mobileChatViewer.style.transform = 'translateX(150%)' : ''
    clearChatMob ? clearChatMob.style.display = 'none' : ''
    if (document.getElementById('readBookBtn')){
        document.getElementById('readBookBtn').style.display = "none";
    }

    if(document.querySelector('.chaptersList')) {
        document.querySelector('.chaptersList').style.display = "block";
    }
    if(document.getElementById('mobNotice')){
        document.getElementById('mobNotice').style.display = 'block'
    }
}
function hideShowMobileDefaultOptions(){
    let state = document.getElementById('defaultHam').getAttribute('data-state')
    if(state=='hide'){
        hideMobileDefaultOptions()
    }else if(state=='show'){
        showMobileDefaultOptions()
    }
}
function hideMobileDefaultOptions(){
    document.querySelectorAll('.defaultOptionCard').forEach(cd => {
        cd.style.display = 'none'
    })
    defaultOptions.style.marginLeft='auto';
    defaultOptions.style.minWidth = "50px";
    defaultOptions.style.width = "50px";
    if(document.querySelector('.dividerLine')){
        document.querySelector('.dividerLine').style.display = 'none'
    }
    document.getElementById('defaultHam').style.display = 'flex'
    document.getElementById('defaultHam').setAttribute('data-state','show')
    document.getElementById('defaultHam').classList.remove('hamburgerMobileClose')
    document.getElementById('defaultHam').classList.add('fa-bars')
    document.getElementById('defaultHam').classList.remove('fa-xmark')
}
function showMobileDefaultOptions(){
    document.querySelectorAll('.defaultOptionCard').forEach(cd => {
        cd.style.display = 'flex'
    })
    defaultOptions.style.width = 'calc(100% - 70px)'
    defaultOptions.style.minWidth = '250px';
    defaultOptions.style.marginLeft='unset'
    if(document.querySelector('.dividerLine')){
        document.querySelector('.dividerLine').style.display = 'flex'
    }
    document.getElementById('defaultHam').classList.add('fa-xmark')
    document.getElementById('defaultHam').classList.add('hamburgerMobileClose')
    document.getElementById('defaultHam').classList.remove('fa-bars')
    document.getElementById('defaultHam').setAttribute('data-state','hide')
}

function showGptLoader(){
    gpt_loader.style.display='flex';
}
function hideGptLoader(){
    gpt_loader.style.display='none';
}
refresHN.addEventListener('click',function (){
    document.querySelector('.apiLoader').style.display = 'flex'
    getHighlightedData()
})
async function getHighlightedData(){
    const apiLoader = document.querySelector('.apiLoader')
    const hnContent = document.getElementById('hnContent')
    openHNSlider()
    let hnContentHTML = ""
    if(loggedInUser){
        const data = await fetch('/wonderpublish/annotateSearch?limit=100&all_fields=1&uri='+gptResId+'&bookId='+gptBookId)
        const res = await data.json()

        if(res && res.rows.length>0 && res.total!==0){
            const highlights = res.rows.reverse()
            highlights.forEach((hn,index)=>{
                hnContentHTML += "<div class='hn-item' id='hn-item-"+hn.id+"' data-item='"+index+"'>";
                if(hn.text!==null){
                    hnContentHTML +="<span class='hnType nColor'>Note</span>"+
                        "<p class='hnText'>"+hn.quote+" </p>"+
                        "<p class='hnote'>"+hn.text+"</p>"
                }else{
                    hnContentHTML +="<span class='hnType hColor'>Highlight</span>"+
                        "<p class='hnText'>"+hn.quote+" </p>"
                }

                hnContentHTML +="</div>"
            })
        }else{
            let opt = "selecting"
            if(mobileView){
                opt = "long pressing"
            }
            hnContentHTML += "<div class='emptyHnText'>" +
                "<p>No highlights or notes yet. </p>" +
                "<p style='font-size: 12px'>Start by <span>"+opt+" text </span>and add highlights and notes.</p>" +
                "</div>"
        }
    }else{
        hnContentHTML += "<div class='emptyHnText'>" +
            "<p>Please login to see highlights and notes. </p>" +
            "<a href='/' style='margin-top: 10px' target='_blank'>Login</a>"+
            "</div>"
    }
    hnContent.innerHTML = hnContentHTML
    apiLoader.style.display = 'none'
}

closeHNSliderBtn.addEventListener('click',function (){
    closeHNSlider();
})

function openHNSlider(){
    const notesAndHighlights = document.querySelector('.notesAndHighlights')
    notesAndHighlights.style.transform = 'translateX(0)';
}
function closeHNSlider(){
    const notesAndHighlights = document.querySelector('.notesAndHighlights')
    notesAndHighlights.style.transform = 'translateX(150%)';
}
function handleDpState(){
    const openCloseDP = document.getElementById('openCloseDP')
    if(openCloseDP){
        const dpState = openCloseDP.getAttribute('data-state')
        if(dpState==="less"){
            defaultOptions.style.height = isContentsLocked ? '100px' : '60px'
            openCloseDP.textContent = "Show more"
            openCloseDP.removeAttribute('data-state')
            openCloseDP.setAttribute('data-state','more')
        }else if(dpState==="more"){
            defaultOptions.style.height = 'auto'
            openCloseDP.textContent = "Show less"
            openCloseDP.removeAttribute('data-state')
            openCloseDP.setAttribute('data-state','less')
            cardClicked = false
        }
    }
}
async function paginateHistory(){
    pageNo = pageNo+10;
    let historyAPI = "/gptLog/find?username="+username+"&resId="+gptResId+"&pageNo="+pageNo
    isTestSeries ? historyAPI+="&quizObjId="+currentMcqObjId :""
    const chatHistory = await fetch(historyAPI)
    let chatHistoryJSON = await chatHistory.json()
    const historyObj = chatHistoryJSON.GptLogs
    if(historyObj.length==0){
        prevChat.innerHTML = "You've reached the end!"
        setTimeout(()=>{
            prevChat.style.display='none';
        },2500)
    }else{
        prevChat.style.display='none';
    }
    if(historyObj.length>0){
        for(let c=0;c<historyObj.length;c++){
            let question
            if(historyObj[c].systemPrompt!=null && historyObj[c].userPrompt==null){
                question = historyObj[c].systemPrompt
            }else if(historyObj[c].systemPrompt==null && historyObj[c].userPrompt!=null){
                question = historyObj[c].userPrompt
            }
            historyObj[c].answer = historyObj[c].response
            historyObj[c].resType = historyObj[c].promptType
            historyObj[c].gptLogId = historyObj[c].id
            updatePrevChat(historyObj[c],question)
        }
    }
}

conversation.addEventListener('scroll', async (e)=>{
    if (isClearingChat) {
        isClearingChat = false;
        return;
    }
    if(conversation.scrollTop==0){
        prevChat.innerHTML = "Loading previous chat..."
        prevChat.style.display='block';
        await paginateHistory()
    }
})

function updatePrevChat(historyObj,question){
    answersCount++
    showAnswer(historyObj,question,false,preChat=true)

    const studentMessageWrapper = document.createElement('div')
    studentMessageWrapper.classList.add('studentMessageWrapper')
    let userMessageHTML = ""
    userMessageHTML +=
        "<div class='message userMessage' id='userQue_"+answersCount+"'>"+question+"</div>"+
        "<div class='studentIcon'>";
    if(profilePic==null || profilePic=='null' || !profilePic){
        userMessageHTML +="<img src='/assets/resource/student-icon.svg'/>";
    }else if(profilePic!=null && profilePic!='null' && profilePic){
        userMessageHTML += "<img src='/funlearn/showProfileImage?id="+userId+"&fileName="+profilePic+"&type=user&imgType=passport'/>";
    }
    userMessageHTML +="</div>";
    studentMessageWrapper.innerHTML  = userMessageHTML
    messages.prepend(studentMessageWrapper)
}

function showChapterLockedModal(){
    let inputHTML = "<div class='feedbackOptions'>" +
        "<div class='closeBtn'>" +
        "<span onclick='closeNotice()'><i class='fa-solid fa-xmark'></i></span>"+
        "</div>"+
        "<div class='showChapterLockedModal'>" +
        "<h4>Unlock Full Access!</h4>"+
        "<p>You're just one step away! Unlock this chapter to continue your reading journey.</p>"+
        "<button class='gptBuyNowBtnNew' onclick='openBookDtlPage()'>Buy Now</button>"+
        "</div>"+
        "</div>";
    feedbackContent.innerHTML = inputHTML
    openModal()
}

function printContent(id) {
    const linkULR = new URL(window.location.href)
    // Get the elements for the question and answer based on the ID
    const answerPrint = document.getElementById('mesPlaceholder_' + id) ||
        document.getElementById('showMCQDiv_' + id) ||
        document.getElementById('showFlashcardDiv_' + id) ||
        document.getElementById('showQnADiv_' + id);
    const questionPrint = document.getElementById('userQue_' + id);
    const printableDiv = document.getElementById('printableDiv');

    // Clone the elements to prevent moving them from their original place
    const clonedAnswer = answerPrint.cloneNode(true);
    const clonedQuestion = questionPrint.cloneNode(true);

    var chapterName=""
    //check if element dropDownValue exists
    if(document.getElementById('dropDownValue')){
        chapterName = document.getElementById('dropDownValue').textContent
    }else if(document.getElementById('chapterSelect')){
        chapterName = document.getElementById('chapterSelect').textContent
    }


    // Clear existing content in printableDiv to prevent duplication
    printableDiv.innerHTML = "";

    // Create branding elements
    const logoDiv = document.createElement('div');
    logoDiv.classList.add('branding');

    const logoImg = document.createElement('img');
    if(gptcustomloader=="Yes"){
        logoImg.src = "/privatelabel/showPrivatelabelImage?siteId="+gptSiteId+"&fileName="+gptloaderpath
    }else{
        logoImg.src = linkULR.origin+'/assets/resource/ibookgpt-logo-light.svg';
    }
    logoImg.style.maxWidth = '150px';
    logoImg.style.marginBottom = '20px';
    logoImg.style.display = 'block';  // Ensures it's treated as a block element
    logoDiv.appendChild(logoImg);

    const siteLink = document.createElement('a');

    siteLink.href = linkULR.origin
    siteLink.textContent = "Visit us at: " +linkULR.host
    siteLink.style.marginRight = '30px'
    logoDiv.appendChild(siteLink)

    // Align logoDiv to the top-left corner
    logoDiv.style.position = 'absolute';
    logoDiv.style.top = '20px';
    logoDiv.style.left = '20px';


    const bookInfoDiv = document.createElement('div');
    bookInfoDiv.classList.add('book-info');
    bookInfoDiv.textContent = "Book: "+bookTitle+" | Chapter: "+chapterName;
    bookInfoDiv.style.fontSize = '14px';
    bookInfoDiv.style.marginTop = '70px';
    bookInfoDiv.style.fontWeight = 'bold';
    // Create content div for question and answer
    const contentDiv = document.createElement('div');
    contentDiv.classList.add('content');
    contentDiv.style.border = '1px solid #ddd';
    contentDiv.style.padding = '20px';
    contentDiv.style.margin = '10px 0 20px 0';  // Adjust margin to avoid logo overlap
    contentDiv.style.borderRadius = '5px';
    contentDiv.style.backgroundColor = '#f9f9f9';

    const questionDiv = document.createElement('div');
    questionDiv.classList.add('question');
    questionDiv.innerHTML = clonedQuestion.innerHTML;
    questionDiv.style.fontWeight = 'bold';
    questionDiv.style.fontSize = '18px';
    questionDiv.style.marginBottom = '10px';
    const answerDiv = document.createElement('div');
    answerDiv.classList.add('response');
    answerDiv.innerHTML = clonedAnswer.innerHTML;
    answerDiv.style.fontSize = '16px';
    answerDiv.style.lineHeight = '1.6';

    contentDiv.appendChild(questionDiv);
    contentDiv.appendChild(answerDiv);

    // Append all elements to the printableDiv
    printableDiv.appendChild(logoDiv);
    printableDiv.appendChild(bookInfoDiv);
    printableDiv.appendChild(contentDiv);

    const watermark = document.createElement('div');
    if(gptcustomloader=="Yes"){
        watermark.innerHTML = gptloaderName
    }else{
        watermark.innerHTML = 'iBookGPT';
    }
    watermark.style.position = 'fixed';
    watermark.style.top = '50%';
    watermark.style.left = '50%';
    watermark.style.transform = 'translate(-50%, -50%) rotate(-45deg)';
    watermark.style.fontSize = '80px';
    watermark.style.color = 'rgba(0, 0, 0, 0.1)';
    watermark.style.pointerEvents = 'none';
    watermark.style.whiteSpace = 'nowrap';
    printableDiv.appendChild(watermark);
    var divContent = printableDiv.innerHTML;

    var printWindow = window.open('', '', 'height=600,width=800');
    printWindow.document.write('<html><head><title>'+chapterName+'</title>');
    printWindow.document.write('<style>');
    printWindow.document.write('@media print {');
    printWindow.document.write('@page { size: A4; margin: 20; }'); // Set margins to 0 to minimize headers and footers
    printWindow.document.write('body { margin: 0; padding: 10mm; -webkit-print-color-adjust: exact; }');
    printWindow.document.write('.branding { position: absolute; top: 20px; left: 20px;width:100%; display:flex;justify-content:space-between;align-items:center;}');
    printWindow.document.write('.branding img { display: block; margin-bottom: 20px; max-width: 150px; }');
    printWindow.document.write('.content { padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9; }');
    printWindow.document.write('.content .question { font-weight: bold; font-size: 16px; margin-bottom: 10px; }');
    printWindow.document.write('.content .response { font-size: 14px; line-height: 1.6; }');
    printWindow.document.write('.watermark { font-size: 100px; opacity: 0.1; }'); // Lighter watermark for print
    printWindow.document.write('}');
    printWindow.document.write('</style>');
    printWindow.document.write('</head><body>');
    printWindow.document.write(divContent);
    printWindow.document.write('</body></html>');
    printWindow.document.close();
    printWindow.print();
    if(!mobileView){
        printWindow.addEventListener('afterprint',()=>{
            printWindow.close();
        })
    }else{
        setTimeout(function() {
            printWindow.close();
        }, 1000);

    }
    printableDiv.innerHTML = "";

}
function togglePromptsDropdown() {
    // const dropdown = document.querySelector('.prompts_dropdown');
    // const ar = document.querySelector('.arrow i');
    // dropdown.classList.toggle('open');
    // ar.classList.toggle('fa-angle-up');
    // ar.classList.toggle('fa-angle-down');
}

const audioInstances = {};
function stopAllAudioExcept(exceptId) {
    Object.entries(audioInstances).forEach(([id, instance]) => {
        if (id !== exceptId && instance.isPlaying) {
            const { audio } = instance;
            const iconBtn = document.getElementById('readOtp_' + id);

            audio.pause();
            iconBtn.innerHTML = "<i class='fa-regular fa-circle-play'></i>";
            audioInstances[id].isPlaying = false;

            if (instance.lastHighlightedWord) {
                instance.lastHighlightedWord.classList.remove('highlight');
                instance.lastHighlightedWord = null;
            }
            if (instance.rafId) {
                cancelAnimationFrame(instance.rafId);
            }
        }
    });
}
async function readResponse(id) {
    const contentElement = document.getElementById('mesPlaceholder_' + id);
    let content = contentElement.innerHTML;
    const iconBtn = document.getElementById('readOtp_' + id);

    if (audioInstances[id]) {
        const { audio, isPlaying } = audioInstances[id];

        if (isPlaying) {
            audio.pause();
            iconBtn.innerHTML = "<i class='fa-regular fa-circle-play'></i>";
            audioInstances[id].isPlaying = false;
        } else {
            stopAllAudioExcept(id);
            audio.play();
            iconBtn.innerHTML = "<i class='fa-regular fa-circle-pause'></i>";
            audioInstances[id].isPlaying = true;
        }
        return;
    }

    stopAllAudioExcept(id);

    const detectedLanguage = detectLanguage(content);

    iconBtn.innerHTML = "<i class='fa-solid fa-spinner'></i>"

    const response = await fetch('/prompt/driveChat',{
        method:"POST",
        body:JSON.stringify({
            content:content,
            type:"voice-mod",
            language: detectedLanguage.code
        }),
        headers:{
            "Content-Type":"application/json"
        }
    })

    if (!response.ok) {
        alert("Something went wrong");
        iconBtn.innerHTML = "<i class='fa-regular fa-circle-play'></i>";
        return;
    }

    const result = await response.json();
    const audioContent = result.audioContent;
    const audio = new Audio("data:audio/ogg;base64," + audioContent);
    const timepoints = result.timepoints;

    const words = [];
    const wordTimings = timepoints.map((point, index) => ({
        time: point.timeSeconds,
        endTime: timepoints[index + 1]?.timeSeconds || Infinity
    }));

    let currentWordIndex = 0;
    let lastHighlightedWord = null;

    function extractWordsFromNode(node) {
        if (node.nodeType === Node.TEXT_NODE) {
            const text = node.nodeValue.trim();
            if (text) {
                const splitWords = text.split(/\s+/);
                const fragment = document.createDocumentFragment();

                splitWords.forEach((word) => {
                    const span = document.createElement('span');
                    span.textContent = word + ' ';
                    words.push(span);
                    fragment.appendChild(span);
                });

                node.parentNode.replaceChild(fragment, node);
            }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            Array.from(node.childNodes).forEach(extractWordsFromNode);
        }
    }

    function findWordIndex(currentTime) {
        let start = 0;
        let end = wordTimings.length - 1;

        while (start <= end) {
            const mid = Math.floor((start + end) / 2);
            const timing = wordTimings[mid];

            if (currentTime >= timing.time && currentTime < timing.endTime) {
                return mid;
            }

            if (currentTime < timing.time) {
                end = mid - 1;
            } else {
                start = mid + 1;
            }
        }

        return -1;
    }

    function updateHighlight() {
        const currentTime = audio.currentTime;
        const newIndex = findWordIndex(currentTime);

        if (newIndex !== currentWordIndex) {
            if (lastHighlightedWord) {
                lastHighlightedWord.classList.remove('highlight');
            }

            if (newIndex >= 0 && newIndex < words.length) {
                words[newIndex].classList.add('highlight');
                lastHighlightedWord = words[newIndex];
                audioInstances[id].lastHighlightedWord = lastHighlightedWord;
            }

            currentWordIndex = newIndex;
        }

        if (!audio.paused && !audio.ended) {
            audioInstances[id].rafId = requestAnimationFrame(updateHighlight);
        }
    }

    extractWordsFromNode(contentElement);

    audioInstances[id] = {
        audio,
        isPlaying: true,
        lastHighlightedWord: null,
        rafId: null,
        words,
        currentWordIndex: 0
    };

    audio.addEventListener('play', () => {
        stopAllAudioExcept(id);
        audioInstances[id].rafId = requestAnimationFrame(updateHighlight);
    });

    audio.addEventListener('pause', () => {
        if (audioInstances[id].rafId) {
            cancelAnimationFrame(audioInstances[id].rafId);
        }
        if (lastHighlightedWord) {
            lastHighlightedWord.classList.remove('highlight');
        }
    });

    audio.addEventListener('ended', () => {
        if (audioInstances[id].rafId) {
            cancelAnimationFrame(audioInstances[id].rafId);
        }
        if (lastHighlightedWord) {
            lastHighlightedWord.classList.remove('highlight');
        }
        currentWordIndex = 0;
        lastHighlightedWord = null;
        iconBtn.innerHTML = "<i class='fa-regular fa-circle-play'></i>";
        delete audioInstances[id];
    });

    audio.addEventListener('seeking', () => {
        if (lastHighlightedWord) {
            lastHighlightedWord.classList.remove('highlight');
        }
        currentWordIndex = 0;
        lastHighlightedWord = null;
    });

    audio.play();
    iconBtn.innerHTML = "<i class='fa-regular fa-circle-pause'></i>";
}
document.addEventListener('DOMContentLoaded', function() {
    const details = document.querySelector('.study-tools');
    document.addEventListener('click', function(e) {
        if (!details.contains(e.target) && details.hasAttribute('open')) {
            details.removeAttribute('open');
        }
    });
    if(details.open && (basePrompts.length>0 || suggestedVideos.length>0)){
        conversation.style.overflow = "hidden"
    }else{
        conversation.style.overflow = "scroll"
    }


    details.addEventListener('toggle', function(e) {
        const isOpen = e.target.open;
        const icon = document.querySelector('.fa-solid');
        if (isOpen) {
            icon.classList.remove('fa-angle-down');
            icon.classList.add('fa-angle-up');
            conversation.style.overflow = "hidden"
        } else {
            icon.classList.remove('fa-angle-up');
            icon.classList.add('fa-angle-down');
            conversation.style.overflow = "scroll"
        }
    });
});
const contentWrapDet = document.querySelector('.study-tools');
const list = document.getElementById('studyToolsList');
function checkScroll() {
    if (list.scrollHeight > contentWrapDet.clientHeight) {
        contentWrapDet.classList.add('is-scrollable');
    } else {
        contentWrapDet.classList.remove('is-scrollable');
    }
}

// Run on content change
const observer = new MutationObserver(checkScroll);
observer.observe(list, { childList: true, subtree: true });

// Run on window resize
window.addEventListener('resize', checkScroll);
const scrollIndicator = document.querySelector('.scroll-indicator');
scrollIndicator.addEventListener('click', function(e) {
    e.stopPropagation();
    contentWrapDet.scrollBy({
        top: 100,
        behavior: 'smooth'
    });
});
function hasLatexOrMath(text) {
    const patterns = [
        /\\\(.*?\\\)/,     // Matches \( ... \)
        /\\\[.*?\\\]/,     // Matches \[ ... \]
        /\$\$.*?\$\$/,     // Matches $$ ... $$
        /\$.*?\$/,         // Matches $ ... $ (inline math)
        /\\[a-zA-Z]+/,     // Matches LaTeX commands like \frac, \sum, etc.
        /[a-zA-Z]+\^\d+/,  // Matches superscripts like a^2, x^3
        /[a-zA-Z]+\_\d+/,  // Matches subscripts like a_1, x_2
        /[a-zA-Z]+ [\+\-\*\/=] [a-zA-Z\d]+/, // Matches basic math like a + b, x = 3
        /\d+\^\d+/,        // Matches numeric powers like 2^3
        /\d+ [\+\-\*\/=] \d+/ // Matches numeric equations like 2 + 2 = 4
    ];

    return patterns.some(pattern => pattern.test(text));
}
